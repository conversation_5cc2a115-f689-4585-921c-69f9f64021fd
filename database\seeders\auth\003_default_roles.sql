-- 默认角色种子数据
-- 环境: 开发环境
-- 描述: 创建默认租户的基础角色

INSERT INTO roles (id, tenant_id, name, code, description, created_at, updated_at) VALUES 
(1, 1, '超级管理员', 'super_admin', '拥有所有权限的超级管理员角色', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 1, '管理员', 'admin', '租户管理员，拥有大部分管理权限', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3, 1, '普通用户', 'user', '普通用户，拥有基础功能权限', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (tenant_id, code) DO NOTHING;

-- 重置序列
SELECT setval('roles_id_seq', (SELECT MAX(id) FROM roles));
