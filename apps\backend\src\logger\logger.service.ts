import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { Logger } from 'winston';
import { logger } from './winston.config';

export interface LogContext {
  traceId?: string;
  correlationId?: string;
  userId?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
  userAgent?: string;
  ip?: string;
  [key: string]: any;
}

@Injectable()
export class LoggerService implements NestLoggerService {
  private readonly logger: Logger = logger;

  log(message: string, context?: LogContext): void {
    this.logger.info(message, context);
  }

  error(message: string, trace?: string, context?: LogContext): void {
    this.logger.error(message, {
      stack: trace,
      ...context,
    });
  }

  warn(message: string, context?: LogContext): void {
    this.logger.warn(message, context);
  }

  debug(message: string, context?: LogContext): void {
    this.logger.debug(message, context);
  }

  verbose(message: string, context?: LogContext): void {
    this.logger.verbose(message, context);
  }

  // 专用方法
  logApiRequest(
    method: string,
    url: string,
    statusCode: number,
    responseTime: number,
    context?: LogContext,
  ): void {
    this.logger.info('API Request', {
      method,
      url,
      statusCode,
      responseTime,
      ...context,
    });
  }

  logApiError(
    method: string,
    url: string,
    statusCode: number,
    error: Error,
    context?: LogContext,
  ): void {
    this.logger.error('API Error', {
      method,
      url,
      statusCode,
      message: error.message,
      stack: error.stack,
      ...context,
    });
  }

  logUserAction(
    userId: string,
    action: string,
    details?: any,
    context?: LogContext,
  ): void {
    this.logger.info('User Action', {
      userId,
      action,
      details,
      ...context,
    });
  }

  logDatabaseOperation(
    operation: string,
    table: string,
    duration?: number,
    context?: LogContext,
  ): void {
    this.logger.info('Database Operation', {
      operation,
      table,
      duration,
      ...context,
    });
  }

  logSecurityEvent(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    details?: any,
    context?: LogContext,
  ): void {
    this.logger.warn('Security Event', {
      event,
      severity,
      details,
      ...context,
    });
  }
}
