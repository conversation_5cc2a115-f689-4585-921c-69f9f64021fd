{"version": 3, "names": ["_path", "data", "require", "_plugin", "_util", "_item", "_config<PERSON><PERSON>n", "_environment", "_options", "_index", "_resolveTargets", "_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "resolveRootMode", "rootDir", "rootMode", "upwardRootDir", "findConfigUpwards", "Object", "assign", "Error", "ROOT_CONFIG_FILENAMES", "join", "code", "dirname", "loadPrivatePartialConfig", "inputOpts", "Array", "isArray", "args", "validate", "envName", "getEnv", "cwd", "root", "caller", "cloneInputAst", "absoluteCwd", "path", "resolve", "absoluteRootDir", "filename", "undefined", "showConfigPath", "resolveShowConfigPath", "context", "showConfig", "config<PERSON><PERSON><PERSON>", "buildRootChain", "merged", "assumptions", "options", "for<PERSON>ach", "opts", "mergeOptions", "targets", "resolveTargets", "babelrc", "configFile", "browserslistConfigFile", "passPerPreset", "plugins", "map", "descriptor", "createItemFromDescriptor", "presets", "fileHandling", "ignore", "config", "files", "loadPartialConfig", "showIgnoredFiles", "_opts", "result", "item", "value", "Plugin", "PartialConfig", "filepath", "constructor", "babelignore", "freeze", "hasFilesystemConfig", "prototype"], "sources": ["../../src/config/partial.ts"], "sourcesContent": ["import path from \"node:path\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport Plugin from \"./plugin.ts\";\nimport { mergeOptions } from \"./util.ts\";\nimport { createItemFromDescriptor } from \"./item.ts\";\nimport { buildRoot<PERSON>hain } from \"./config-chain.ts\";\nimport type { ConfigContext, FileHandling } from \"./config-chain.ts\";\nimport { getEnv } from \"./helpers/environment.ts\";\nimport { validate } from \"./validation/options.ts\";\n\nimport type {\n  ValidatedOptions,\n  NormalizedOptions,\n  RootMode,\n  InputOptions,\n} from \"./validation/options.ts\";\n\nimport {\n  findConfigUpwards,\n  resolveShowConfigPath,\n  ROOT_CONFIG_FILENAMES,\n} from \"./files/index.ts\";\nimport type { ConfigFile, IgnoreFile } from \"./files/index.ts\";\nimport { resolveTargets } from \"./resolve-targets.ts\";\n\nfunction resolveRootMode(rootDir: string, rootMode: RootMode): string {\n  switch (rootMode) {\n    case \"root\":\n      return rootDir;\n\n    case \"upward-optional\": {\n      const upwardRootDir = findConfigUpwards(rootDir);\n      return upwardRootDir === null ? rootDir : upwardRootDir;\n    }\n\n    case \"upward\": {\n      const upwardRootDir = findConfigUpwards(rootDir);\n      if (upwardRootDir !== null) return upwardRootDir;\n\n      throw Object.assign(\n        new Error(\n          `Babel was run with rootMode:\"upward\" but a root could not ` +\n            `be found when searching upward from \"${rootDir}\".\\n` +\n            `One of the following config files must be in the directory tree: ` +\n            `\"${ROOT_CONFIG_FILENAMES.join(\", \")}\".`,\n        ) as any,\n        {\n          code: \"BABEL_ROOT_NOT_FOUND\",\n          dirname: rootDir,\n        },\n      );\n    }\n    default:\n      throw new Error(`Assertion failure - unknown rootMode value.`);\n  }\n}\n\nexport type PrivPartialConfig = {\n  showIgnoredFiles?: boolean;\n  options: NormalizedOptions;\n  context: ConfigContext;\n  babelrc: ConfigFile | void;\n  config: ConfigFile | void;\n  ignore: IgnoreFile | void;\n  fileHandling: FileHandling;\n  files: Set<string>;\n};\n\nexport default function* loadPrivatePartialConfig(\n  inputOpts: InputOptions,\n): Handler<PrivPartialConfig | null> {\n  if (\n    inputOpts != null &&\n    (typeof inputOpts !== \"object\" || Array.isArray(inputOpts))\n  ) {\n    throw new Error(\"Babel options must be an object, null, or undefined\");\n  }\n\n  const args = inputOpts ? validate(\"arguments\", inputOpts) : {};\n\n  const {\n    envName = getEnv(),\n    cwd = \".\",\n    root: rootDir = \".\",\n    rootMode = \"root\",\n    caller,\n    cloneInputAst = true,\n  } = args;\n  const absoluteCwd = path.resolve(cwd);\n  const absoluteRootDir = resolveRootMode(\n    path.resolve(absoluteCwd, rootDir),\n    rootMode,\n  );\n\n  const filename =\n    typeof args.filename === \"string\"\n      ? path.resolve(cwd, args.filename)\n      : undefined;\n\n  const showConfigPath = yield* resolveShowConfigPath(absoluteCwd);\n\n  const context: ConfigContext = {\n    filename,\n    cwd: absoluteCwd,\n    root: absoluteRootDir,\n    envName,\n    caller,\n    showConfig: showConfigPath === filename,\n  };\n\n  const configChain = yield* buildRootChain(args, context);\n  if (!configChain) return null;\n\n  const merged: ValidatedOptions = {\n    assumptions: {},\n  };\n  configChain.options.forEach(opts => {\n    mergeOptions(merged as any, opts);\n  });\n\n  const options: NormalizedOptions = {\n    ...merged,\n    targets: resolveTargets(merged, absoluteRootDir),\n\n    // Tack the passes onto the object itself so that, if this object is\n    // passed back to Babel a second time, it will be in the right structure\n    // to not change behavior.\n    cloneInputAst,\n    babelrc: false,\n    configFile: false,\n    browserslistConfigFile: false,\n    passPerPreset: false,\n    envName: context.envName,\n    cwd: context.cwd,\n    root: context.root,\n    rootMode: \"root\",\n    filename:\n      typeof context.filename === \"string\" ? context.filename : undefined,\n\n    plugins: configChain.plugins.map(descriptor =>\n      createItemFromDescriptor(descriptor),\n    ),\n    presets: configChain.presets.map(descriptor =>\n      createItemFromDescriptor(descriptor),\n    ),\n  };\n\n  return {\n    options,\n    context,\n    fileHandling: configChain.fileHandling,\n    ignore: configChain.ignore,\n    babelrc: configChain.babelrc,\n    config: configChain.config,\n    files: configChain.files,\n  };\n}\n\nexport function* loadPartialConfig(\n  opts?: InputOptions,\n): Handler<PartialConfig | null> {\n  let showIgnoredFiles = false;\n  // We only extract showIgnoredFiles if opts is an object, so that\n  // loadPrivatePartialConfig can throw the appropriate error if it's not.\n  if (typeof opts === \"object\" && opts !== null && !Array.isArray(opts)) {\n    ({ showIgnoredFiles, ...opts } = opts);\n  }\n\n  const result: PrivPartialConfig | undefined | null =\n    yield* loadPrivatePartialConfig(opts);\n  if (!result) return null;\n\n  const { options, babelrc, ignore, config, fileHandling, files } = result;\n\n  if (fileHandling === \"ignored\" && !showIgnoredFiles) {\n    return null;\n  }\n\n  (options.plugins || []).forEach(item => {\n    // @ts-expect-error todo(flow->ts): better type annotation for `item.value`\n    if (item.value instanceof Plugin) {\n      throw new Error(\n        \"Passing cached plugin instances is not supported in \" +\n          \"babel.loadPartialConfig()\",\n      );\n    }\n  });\n\n  return new PartialConfig(\n    options,\n    babelrc ? babelrc.filepath : undefined,\n    ignore ? ignore.filepath : undefined,\n    config ? config.filepath : undefined,\n    fileHandling,\n    files,\n  );\n}\n\nexport type { PartialConfig };\n\nclass PartialConfig {\n  /**\n   * These properties are public, so any changes to them should be considered\n   * a breaking change to Babel's API.\n   */\n  options: NormalizedOptions;\n  babelrc: string | void;\n  babelignore: string | void;\n  config: string | void;\n  fileHandling: FileHandling;\n  files: Set<string>;\n\n  constructor(\n    options: NormalizedOptions,\n    babelrc: string | void,\n    ignore: string | void,\n    config: string | void,\n    fileHandling: FileHandling,\n    files: Set<string>,\n  ) {\n    this.options = options;\n    this.babelignore = ignore;\n    this.babelrc = babelrc;\n    this.config = config;\n    this.fileHandling = fileHandling;\n    this.files = files;\n\n    // Freeze since this is a public API and it should be extremely obvious that\n    // reassigning properties on here does nothing.\n    Object.freeze(this);\n  }\n\n  /**\n   * Returns true if there is a config file in the filesystem for this config.\n   */\n  hasFilesystemConfig(): boolean {\n    return this.babelrc !== undefined || this.config !== undefined;\n  }\n}\nObject.freeze(PartialConfig.prototype);\n"], "mappings": ";;;;;;;AAAA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAE,OAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AAEA,IAAAK,YAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AASA,IAAAO,MAAA,GAAAP,OAAA;AAMA,IAAAQ,eAAA,GAAAR,OAAA;AAAsD,MAAAS,SAAA;AAAA,SAAAC,8BAAAC,CAAA,EAAAC,CAAA,gBAAAD,CAAA,iBAAAE,CAAA,gBAAAC,CAAA,IAAAH,CAAA,SAAAI,cAAA,CAAAC,IAAA,CAAAL,CAAA,EAAAG,CAAA,gBAAAF,CAAA,CAAAK,OAAA,CAAAH,CAAA,aAAAD,CAAA,CAAAC,CAAA,IAAAH,CAAA,CAAAG,CAAA,YAAAD,CAAA;AAEtD,SAASK,eAAeA,CAACC,OAAe,EAAEC,QAAkB,EAAU;EACpE,QAAQA,QAAQ;IACd,KAAK,MAAM;MACT,OAAOD,OAAO;IAEhB,KAAK,iBAAiB;MAAE;QACtB,MAAME,aAAa,GAAG,IAAAC,wBAAiB,EAACH,OAAO,CAAC;QAChD,OAAOE,aAAa,KAAK,IAAI,GAAGF,OAAO,GAAGE,aAAa;MACzD;IAEA,KAAK,QAAQ;MAAE;QACb,MAAMA,aAAa,GAAG,IAAAC,wBAAiB,EAACH,OAAO,CAAC;QAChD,IAAIE,aAAa,KAAK,IAAI,EAAE,OAAOA,aAAa;QAEhD,MAAME,MAAM,CAACC,MAAM,CACjB,IAAIC,KAAK,CACP,4DAA4D,GAC1D,wCAAwCN,OAAO,MAAM,GACrD,mEAAmE,GACnE,IAAIO,4BAAqB,CAACC,IAAI,CAAC,IAAI,CAAC,IACxC,CAAC,EACD;UACEC,IAAI,EAAE,sBAAsB;UAC5BC,OAAO,EAAEV;QACX,CACF,CAAC;MACH;IACA;MACE,MAAM,IAAIM,KAAK,CAAC,6CAA6C,CAAC;EAClE;AACF;AAae,UAAUK,wBAAwBA,CAC/CC,SAAuB,EACY;EACnC,IACEA,SAAS,IAAI,IAAI,KAChB,OAAOA,SAAS,KAAK,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,CAAC,EAC3D;IACA,MAAM,IAAIN,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAMS,IAAI,GAAGH,SAAS,GAAG,IAAAI,iBAAQ,EAAC,WAAW,EAAEJ,SAAS,CAAC,GAAG,CAAC,CAAC;EAE9D,MAAM;IACJK,OAAO,GAAG,IAAAC,mBAAM,EAAC,CAAC;IAClBC,GAAG,GAAG,GAAG;IACTC,IAAI,EAAEpB,OAAO,GAAG,GAAG;IACnBC,QAAQ,GAAG,MAAM;IACjBoB,MAAM;IACNC,aAAa,GAAG;EAClB,CAAC,GAAGP,IAAI;EACR,MAAMQ,WAAW,GAAGC,MAAGA,CAAC,CAACC,OAAO,CAACN,GAAG,CAAC;EACrC,MAAMO,eAAe,GAAG3B,eAAe,CACrCyB,MAAGA,CAAC,CAACC,OAAO,CAACF,WAAW,EAAEvB,OAAO,CAAC,EAClCC,QACF,CAAC;EAED,MAAM0B,QAAQ,GACZ,OAAOZ,IAAI,CAACY,QAAQ,KAAK,QAAQ,GAC7BH,MAAGA,CAAC,CAACC,OAAO,CAACN,GAAG,EAAEJ,IAAI,CAACY,QAAQ,CAAC,GAChCC,SAAS;EAEf,MAAMC,cAAc,GAAG,OAAO,IAAAC,4BAAqB,EAACP,WAAW,CAAC;EAEhE,MAAMQ,OAAsB,GAAG;IAC7BJ,QAAQ;IACRR,GAAG,EAAEI,WAAW;IAChBH,IAAI,EAAEM,eAAe;IACrBT,OAAO;IACPI,MAAM;IACNW,UAAU,EAAEH,cAAc,KAAKF;EACjC,CAAC;EAED,MAAMM,WAAW,GAAG,OAAO,IAAAC,2BAAc,EAACnB,IAAI,EAAEgB,OAAO,CAAC;EACxD,IAAI,CAACE,WAAW,EAAE,OAAO,IAAI;EAE7B,MAAME,MAAwB,GAAG;IAC/BC,WAAW,EAAE,CAAC;EAChB,CAAC;EACDH,WAAW,CAACI,OAAO,CAACC,OAAO,CAACC,IAAI,IAAI;IAClC,IAAAC,kBAAY,EAACL,MAAM,EAASI,IAAI,CAAC;EACnC,CAAC,CAAC;EAEF,MAAMF,OAA0B,GAAAjC,MAAA,CAAAC,MAAA,KAC3B8B,MAAM;IACTM,OAAO,EAAE,IAAAC,8BAAc,EAACP,MAAM,EAAET,eAAe,CAAC;IAKhDJ,aAAa;IACbqB,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBC,sBAAsB,EAAE,KAAK;IAC7BC,aAAa,EAAE,KAAK;IACpB7B,OAAO,EAAEc,OAAO,CAACd,OAAO;IACxBE,GAAG,EAAEY,OAAO,CAACZ,GAAG;IAChBC,IAAI,EAAEW,OAAO,CAACX,IAAI;IAClBnB,QAAQ,EAAE,MAAM;IAChB0B,QAAQ,EACN,OAAOI,OAAO,CAACJ,QAAQ,KAAK,QAAQ,GAAGI,OAAO,CAACJ,QAAQ,GAAGC,SAAS;IAErEmB,OAAO,EAAEd,WAAW,CAACc,OAAO,CAACC,GAAG,CAACC,UAAU,IACzC,IAAAC,8BAAwB,EAACD,UAAU,CACrC,CAAC;IACDE,OAAO,EAAElB,WAAW,CAACkB,OAAO,CAACH,GAAG,CAACC,UAAU,IACzC,IAAAC,8BAAwB,EAACD,UAAU,CACrC;EAAC,EACF;EAED,OAAO;IACLZ,OAAO;IACPN,OAAO;IACPqB,YAAY,EAAEnB,WAAW,CAACmB,YAAY;IACtCC,MAAM,EAAEpB,WAAW,CAACoB,MAAM;IAC1BV,OAAO,EAAEV,WAAW,CAACU,OAAO;IAC5BW,MAAM,EAAErB,WAAW,CAACqB,MAAM;IAC1BC,KAAK,EAAEtB,WAAW,CAACsB;EACrB,CAAC;AACH;AAEO,UAAUC,iBAAiBA,CAChCjB,IAAmB,EACY;EAC/B,IAAIkB,gBAAgB,GAAG,KAAK;EAG5B,IAAI,OAAOlB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI,CAAC1B,KAAK,CAACC,OAAO,CAACyB,IAAI,CAAC,EAAE;IAAA,IAAAmB,KAAA,GACpCnB,IAAI;IAAA,CAApC;MAAEkB;IAA0B,CAAC,GAAAC,KAAO;IAAbnB,IAAI,GAAAhD,6BAAA,CAAAmE,KAAA,EAAApE,SAAA;IAAAoE,KAAA;EAC9B;EAEA,MAAMC,MAA4C,GAChD,OAAOhD,wBAAwB,CAAC4B,IAAI,CAAC;EACvC,IAAI,CAACoB,MAAM,EAAE,OAAO,IAAI;EAExB,MAAM;IAAEtB,OAAO;IAAEM,OAAO;IAAEU,MAAM;IAAEC,MAAM;IAAEF,YAAY;IAAEG;EAAM,CAAC,GAAGI,MAAM;EAExE,IAAIP,YAAY,KAAK,SAAS,IAAI,CAACK,gBAAgB,EAAE;IACnD,OAAO,IAAI;EACb;EAEA,CAACpB,OAAO,CAACU,OAAO,IAAI,EAAE,EAAET,OAAO,CAACsB,IAAI,IAAI;IAEtC,IAAIA,IAAI,CAACC,KAAK,YAAYC,eAAM,EAAE;MAChC,MAAM,IAAIxD,KAAK,CACb,sDAAsD,GACpD,2BACJ,CAAC;IACH;EACF,CAAC,CAAC;EAEF,OAAO,IAAIyD,aAAa,CACtB1B,OAAO,EACPM,OAAO,GAAGA,OAAO,CAACqB,QAAQ,GAAGpC,SAAS,EACtCyB,MAAM,GAAGA,MAAM,CAACW,QAAQ,GAAGpC,SAAS,EACpC0B,MAAM,GAAGA,MAAM,CAACU,QAAQ,GAAGpC,SAAS,EACpCwB,YAAY,EACZG,KACF,CAAC;AACH;AAIA,MAAMQ,aAAa,CAAC;EAYlBE,WAAWA,CACT5B,OAA0B,EAC1BM,OAAsB,EACtBU,MAAqB,EACrBC,MAAqB,EACrBF,YAA0B,EAC1BG,KAAkB,EAClB;IAAA,KAdFlB,OAAO;IAAA,KACPM,OAAO;IAAA,KACPuB,WAAW;IAAA,KACXZ,MAAM;IAAA,KACNF,YAAY;IAAA,KACZG,KAAK;IAUH,IAAI,CAAClB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC6B,WAAW,GAAGb,MAAM;IACzB,IAAI,CAACV,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACW,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACF,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACG,KAAK,GAAGA,KAAK;IAIlBnD,MAAM,CAAC+D,MAAM,CAAC,IAAI,CAAC;EACrB;EAKAC,mBAAmBA,CAAA,EAAY;IAC7B,OAAO,IAAI,CAACzB,OAAO,KAAKf,SAAS,IAAI,IAAI,CAAC0B,MAAM,KAAK1B,SAAS;EAChE;AACF;AACAxB,MAAM,CAAC+D,MAAM,CAACJ,aAAa,CAACM,SAAS,CAAC;AAAC", "ignoreList": []}