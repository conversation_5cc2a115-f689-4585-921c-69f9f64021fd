#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');

console.log('🔄 开始重置数据库...');

try {
    // 停止相关服务
    console.log('⏹️  停止数据库服务...');
    execSync('docker-compose -f docker-compose.dev.yml stop db', { stdio: 'inherit' });
    
    // 删除数据库容器和卷
    console.log('🗑️  删除数据库容器和卷...');
    execSync('docker-compose -f docker-compose.dev.yml rm -f db', { stdio: 'inherit' });
    execSync('docker volume rm wht-saas_pg_data 2>/dev/null || true', { stdio: 'inherit' });
    
    // 重新构建并启动数据库
    console.log('🔨 重新构建数据库容器...');
    execSync('docker-compose -f docker-compose.dev.yml up -d --build db', { stdio: 'inherit' });
    
    // 等待数据库启动
    console.log('⏳ 等待数据库启动...');
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
        try {
            execSync('docker-compose -f docker-compose.dev.yml exec -T db pg_isready -U user -d yourdb', { stdio: 'pipe' });
            break;
        } catch (error) {
            attempts++;
            if (attempts >= maxAttempts) {
                throw new Error('数据库启动超时');
            }
            console.log(`等待数据库启动... (${attempts}/${maxAttempts})`);
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    console.log('✅ 数据库重置完成!');
    console.log('📊 数据库状态:');
    execSync('docker-compose -f docker-compose.dev.yml ps db', { stdio: 'inherit' });
    
} catch (error) {
    console.error('❌ 数据库重置失败:', error.message);
    process.exit(1);
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
