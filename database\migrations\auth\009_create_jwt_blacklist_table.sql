-- 创建JWT黑名单表
-- 创建时间: 2024-01-01
-- 描述: JWT令牌黑名单管理，用于令牌撤销和安全控制

CREATE TABLE IF NOT EXISTS jwt_blacklist (
    id BIGSERIAL PRIMARY KEY,
    jti VARCHAR(255) NOT NULL UNIQUE COMMENT 'JWT唯一标识符（JWT ID）',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    reason VARCHAR(500) NOT NULL COMMENT '拉黑原因',
    expired_at TIMESTAMP NOT NULL COMMENT 'JWT过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '拉黑时间',
    
    -- 外键约束
    CONSTRAINT fk_jwt_blacklist_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建索引
CREATE UNIQUE INDEX idx_jwt_blacklist_jti ON jwt_blacklist(jti);
CREATE INDEX idx_jwt_blacklist_user_id ON jwt_blacklist(user_id);
CREATE INDEX idx_jwt_blacklist_expired_at ON jwt_blacklist(expired_at);
CREATE INDEX idx_jwt_blacklist_created_at ON jwt_blacklist(created_at);

-- 创建复合索引（查询用户的有效黑名单令牌）
CREATE INDEX idx_jwt_blacklist_user_expired ON jwt_blacklist(user_id, expired_at);

-- 添加表注释
COMMENT ON TABLE jwt_blacklist IS 'JWT黑名单表 - 用于JWT令牌撤销和安全控制';

-- 添加字段注释
COMMENT ON COLUMN jwt_blacklist.id IS 'JWT黑名单ID，主键';
COMMENT ON COLUMN jwt_blacklist.jti IS 'JWT唯一标识符，全局唯一';
COMMENT ON COLUMN jwt_blacklist.user_id IS '用户ID，外键关联users表';
COMMENT ON COLUMN jwt_blacklist.reason IS 'JWT拉黑原因描述';
COMMENT ON COLUMN jwt_blacklist.expired_at IS 'JWT原始过期时间';
COMMENT ON COLUMN jwt_blacklist.created_at IS 'JWT拉黑时间';

-- 创建清理过期JWT黑名单的函数
CREATE OR REPLACE FUNCTION cleanup_expired_jwt_blacklist()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM jwt_blacklist WHERE expired_at < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 添加函数注释
COMMENT ON FUNCTION cleanup_expired_jwt_blacklist() IS '清理过期的JWT黑名单记录，返回删除的记录数';
