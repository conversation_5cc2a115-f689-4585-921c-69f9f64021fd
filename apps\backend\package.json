{"name": "nest-myapp-template", "version": "0.0.9", "description": "nestjs application", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "dev": "nest start --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/swagger": "^7.0.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "winston": "^3.11.0", "nest-winston": "^1.9.4", "winston-daily-rotate-file": "^4.7.1", "winston-elasticsearch": "^0.17.4", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.0", "rxjs": "^7.8.0", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/supertest": "^6.0.0", "@types/winston": "^2.4.4", "@types/uuid": "^9.0.0", "eslint": "^8.42.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "supertest": "^6.3.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.0", "ts-node": "^10.9.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.spec.ts", "!**/*.interface.ts", "!**/node_modules/**"], "coverageDirectory": "../coverage", "coverageReporters": ["text", "lcov", "html"], "testEnvironment": "node"}, "publishConfig": {"registry": "https://registry.npmjs.org"}, "repository": {"type": "git", "url": "https://github.com/tsdk/create-nestjs-app.git", "directory": "packages/nest-myapp-template"}, "keywords": ["<PERSON><PERSON><PERSON>", "typescript"], "author": "zaczheng <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/tsdk/create-nestjs-app/issues"}, "homepage": "https://github.com/tsdk/create-nestjs-app", "gitHead": "ac9072902aff8d26df5ab02f85f25af7d1b85bb8"}