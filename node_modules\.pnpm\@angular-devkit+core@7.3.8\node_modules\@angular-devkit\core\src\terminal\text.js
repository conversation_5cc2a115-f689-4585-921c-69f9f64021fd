"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const caps = require("./caps");
const colors_1 = require("./colors");
exports.reset = caps.stdout.colors ? colors_1.colors.reset : (x) => x;
exports.bold = caps.stdout.colors ? colors_1.colors.bold : (x) => x;
exports.dim = caps.stdout.colors ? colors_1.colors.dim : (x) => x;
exports.italic = caps.stdout.colors ? colors_1.colors.italic : (x) => x;
exports.underline = caps.stdout.colors ? colors_1.colors.underline : (x) => x;
exports.inverse = caps.stdout.colors ? colors_1.colors.inverse : (x) => x;
exports.hidden = caps.stdout.colors ? colors_1.colors.hidden : (x) => x;
exports.strikethrough = caps.stdout.colors ? colors_1.colors.strikethrough : (x) => x;
exports.black = caps.stdout.colors ? colors_1.colors.black : (x) => x;
exports.red = caps.stdout.colors ? colors_1.colors.red : (x) => x;
exports.green = caps.stdout.colors ? colors_1.colors.green : (x) => x;
exports.yellow = caps.stdout.colors ? colors_1.colors.yellow : (x) => x;
exports.blue = caps.stdout.colors ? colors_1.colors.blue : (x) => x;
exports.magenta = caps.stdout.colors ? colors_1.colors.magenta : (x) => x;
exports.cyan = caps.stdout.colors ? colors_1.colors.cyan : (x) => x;
exports.white = caps.stdout.colors ? colors_1.colors.white : (x) => x;
exports.grey = caps.stdout.colors ? colors_1.colors.gray : (x) => x;
exports.gray = caps.stdout.colors ? colors_1.colors.gray : (x) => x;
exports.bgBlack = caps.stdout.colors ? colors_1.colors.bgBlack : (x) => x;
exports.bgRed = caps.stdout.colors ? colors_1.colors.bgRed : (x) => x;
exports.bgGreen = caps.stdout.colors ? colors_1.colors.bgGreen : (x) => x;
exports.bgYellow = caps.stdout.colors ? colors_1.colors.bgYellow : (x) => x;
exports.bgBlue = caps.stdout.colors ? colors_1.colors.bgBlue : (x) => x;
exports.bgMagenta = caps.stdout.colors ? colors_1.colors.bgMagenta : (x) => x;
exports.bgCyan = caps.stdout.colors ? colors_1.colors.bgCyan : (x) => x;
exports.bgWhite = caps.stdout.colors ? colors_1.colors.bgWhite : (x) => x;
//# sourceMappingURL=data:application/json;base64,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