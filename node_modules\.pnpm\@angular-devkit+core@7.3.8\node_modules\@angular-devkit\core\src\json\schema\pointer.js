"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function buildJsonPointer(fragments) {
    return ('/' + fragments.map(f => {
        return f.replace(/~/g, '~0')
            .replace(/\//g, '~1');
    }).join('/'));
}
exports.buildJsonPointer = buildJsonPointer;
function joinJsonPointer(root, ...others) {
    if (root == '/') {
        return buildJsonPointer(others);
    }
    return (root + buildJsonPointer(others));
}
exports.joinJsonPointer = joinJsonPointer;
function parseJsonPointer(pointer) {
    if (pointer === '') {
        return [];
    }
    if (pointer.charAt(0) !== '/') {
        throw new Error('Relative pointer: ' + pointer);
    }
    return pointer.substring(1).split(/\//).map(str => str.replace(/~1/g, '/').replace(/~0/g, '~'));
}
exports.parseJsonPointer = parseJsonPointer;
//# sourceMappingURL=data:application/json;base64,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