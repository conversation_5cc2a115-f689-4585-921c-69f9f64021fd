"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// tslint:disable:no-implicit-dependencies
const benchmark_1 = require("@_/benchmark");
const path_1 = require("./path");
const seedrandom = require('seedrandom');
const p1 = '/b/././a/tt/../../../a/b/./d/../c';
const p2 = '/a/tt/../../../a/b/./d';
const numRandomIter = 10000;
describe('Virtual FS Path', () => {
    benchmark_1.benchmark('join', () => path_1.join(path_1.normalize(p1), p2));
    describe('normalize', () => {
        let rng;
        let cases;
        // Math.random() doesn't allow us to set a seed, so we use a library.
        beforeEach(() => {
            rng = seedrandom('some fixed value');
            function _str(len) {
                let r = '';
                const space = 'abcdefghijklmnopqrstuvwxyz0123456789';
                for (let i = 0; i < len; i++) {
                    r += space[Math.floor(rng() * space.length)];
                }
                return r;
            }
            // Build test cases.
            cases = new Array(numRandomIter)
                .fill(0)
                .map(() => {
                return new Array(Math.floor(rng() * 20 + 5))
                    .fill(0)
                    .map(() => _str(rng() * 20 + 3))
                    .join('/');
            });
            path_1.resetNormalizeCache();
        });
        describe('random (0 cache hits)', () => {
            benchmark_1.benchmark('', i => path_1.normalize(cases[i]), i => path_1.noCacheNormalize(cases[i]));
        });
        describe('random (10% cache hits)', () => {
            beforeEach(() => {
                cases = cases.map(x => (rng() < 0.1) ? cases[0] : x);
            });
            benchmark_1.benchmark('', i => path_1.normalize(cases[i]), i => path_1.noCacheNormalize(cases[i]));
        });
        describe('random (30% cache hits)', () => {
            beforeEach(() => {
                cases = cases.map(x => (rng() < 0.3) ? cases[0] : x);
            });
            benchmark_1.benchmark('', i => path_1.normalize(cases[i]), i => path_1.noCacheNormalize(cases[i]));
        });
        describe('random (50% cache hits)', () => {
            beforeEach(() => {
                cases = cases.map(x => (rng() < 0.5) ? cases[0] : x);
            });
            benchmark_1.benchmark('', i => path_1.normalize(cases[i]), i => path_1.noCacheNormalize(cases[i]));
        });
        describe('random (80% cache hits)', () => {
            beforeEach(() => {
                cases = cases.map(x => (rng() < 0.8) ? cases[0] : x);
            });
            benchmark_1.benchmark('', i => path_1.normalize(cases[i]), i => path_1.noCacheNormalize(cases[i]));
        });
    });
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicGF0aF9iZW5jaG1hcmsuanMiLCJzb3VyY2VSb290IjoiLi8iLCJzb3VyY2VzIjpbInBhY2thZ2VzL2FuZ3VsYXJfZGV2a2l0L2NvcmUvc3JjL3ZpcnR1YWwtZnMvcGF0aF9iZW5jaG1hcmsudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7QUFBQTs7Ozs7O0dBTUc7QUFDSCwwQ0FBMEM7QUFDMUMsNENBQXlDO0FBQ3pDLGlDQUFnRjtBQUVoRixNQUFNLFVBQVUsR0FBRyxPQUFPLENBQUMsWUFBWSxDQUFDLENBQUM7QUFHekMsTUFBTSxFQUFFLEdBQUcsbUNBQW1DLENBQUM7QUFDL0MsTUFBTSxFQUFFLEdBQUcsd0JBQXdCLENBQUM7QUFHcEMsTUFBTSxhQUFhLEdBQUcsS0FBSyxDQUFDO0FBRzVCLFFBQVEsQ0FBQyxpQkFBaUIsRUFBRSxHQUFHLEVBQUU7SUFDL0IscUJBQVMsQ0FBQyxNQUFNLEVBQUUsR0FBRyxFQUFFLENBQUMsV0FBSSxDQUFDLGdCQUFTLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUVqRCxRQUFRLENBQUMsV0FBVyxFQUFFLEdBQUcsRUFBRTtRQUN6QixJQUFJLEdBQWlCLENBQUM7UUFDdEIsSUFBSSxLQUFlLENBQUM7UUFFcEIscUVBQXFFO1FBQ3JFLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxHQUFHLEdBQUcsVUFBVSxDQUFDLGtCQUFrQixDQUFDLENBQUM7WUFFckMsU0FBUyxJQUFJLENBQUMsR0FBVztnQkFDdkIsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDO2dCQUNYLE1BQU0sS0FBSyxHQUFHLHNDQUFzQyxDQUFDO2dCQUNyRCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsR0FBRyxFQUFFLENBQUMsRUFBRSxFQUFFO29CQUM1QixDQUFDLElBQUksS0FBSyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxFQUFFLEdBQUcsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUM7aUJBQzlDO2dCQUVELE9BQU8sQ0FBQyxDQUFDO1lBQ1gsQ0FBQztZQUVELG9CQUFvQjtZQUNwQixLQUFLLEdBQUcsSUFBSSxLQUFLLENBQUMsYUFBYSxDQUFDO2lCQUM3QixJQUFJLENBQUMsQ0FBQyxDQUFDO2lCQUNQLEdBQUcsQ0FBQyxHQUFHLEVBQUU7Z0JBQ1IsT0FBTyxJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxDQUFDLENBQUMsQ0FBQztxQkFDekMsSUFBSSxDQUFDLENBQUMsQ0FBQztxQkFDUCxHQUFHLENBQUMsR0FBRyxFQUFFLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxDQUFDLENBQUMsQ0FBQztxQkFDL0IsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ2YsQ0FBQyxDQUFDLENBQUM7WUFFTCwwQkFBbUIsRUFBRSxDQUFDO1FBQ3hCLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLHVCQUF1QixFQUFFLEdBQUcsRUFBRTtZQUNyQyxxQkFBUyxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLGdCQUFTLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyx1QkFBZ0IsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzNFLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLHlCQUF5QixFQUFFLEdBQUcsRUFBRTtZQUN2QyxVQUFVLENBQUMsR0FBRyxFQUFFO2dCQUNkLEtBQUssR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHLEVBQUUsR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN2RCxDQUFDLENBQUMsQ0FBQztZQUNILHFCQUFTLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsZ0JBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLHVCQUFnQixDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDM0UsQ0FBQyxDQUFDLENBQUM7UUFFSCxRQUFRLENBQUMseUJBQXlCLEVBQUUsR0FBRyxFQUFFO1lBQ3ZDLFVBQVUsQ0FBQyxHQUFHLEVBQUU7Z0JBQ2QsS0FBSyxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUcsRUFBRSxHQUFHLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3ZELENBQUMsQ0FBQyxDQUFDO1lBQ0gscUJBQVMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxnQkFBUyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsdUJBQWdCLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUMzRSxDQUFDLENBQUMsQ0FBQztRQUVILFFBQVEsQ0FBQyx5QkFBeUIsRUFBRSxHQUFHLEVBQUU7WUFDdkMsVUFBVSxDQUFDLEdBQUcsRUFBRTtnQkFDZCxLQUFLLEdBQUcsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxFQUFFLEdBQUcsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDdkQsQ0FBQyxDQUFDLENBQUM7WUFDSCxxQkFBUyxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLGdCQUFTLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyx1QkFBZ0IsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzNFLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLHlCQUF5QixFQUFFLEdBQUcsRUFBRTtZQUN2QyxVQUFVLENBQUMsR0FBRyxFQUFFO2dCQUNkLEtBQUssR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHLEVBQUUsR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN2RCxDQUFDLENBQUMsQ0FBQztZQUNILHFCQUFTLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsZ0JBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLHVCQUFnQixDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDM0UsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztBQUNMLENBQUMsQ0FBQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBJbmMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuLy8gdHNsaW50OmRpc2FibGU6bm8taW1wbGljaXQtZGVwZW5kZW5jaWVzXG5pbXBvcnQgeyBiZW5jaG1hcmsgfSBmcm9tICdAXy9iZW5jaG1hcmsnO1xuaW1wb3J0IHsgam9pbiwgbm9DYWNoZU5vcm1hbGl6ZSwgbm9ybWFsaXplLCByZXNldE5vcm1hbGl6ZUNhY2hlIH0gZnJvbSAnLi9wYXRoJztcblxuY29uc3Qgc2VlZHJhbmRvbSA9IHJlcXVpcmUoJ3NlZWRyYW5kb20nKTtcblxuXG5jb25zdCBwMSA9ICcvYi8uLy4vYS90dC8uLi8uLi8uLi9hL2IvLi9kLy4uL2MnO1xuY29uc3QgcDIgPSAnL2EvdHQvLi4vLi4vLi4vYS9iLy4vZCc7XG5cblxuY29uc3QgbnVtUmFuZG9tSXRlciA9IDEwMDAwO1xuXG5cbmRlc2NyaWJlKCdWaXJ0dWFsIEZTIFBhdGgnLCAoKSA9PiB7XG4gIGJlbmNobWFyaygnam9pbicsICgpID0+IGpvaW4obm9ybWFsaXplKHAxKSwgcDIpKTtcblxuICBkZXNjcmliZSgnbm9ybWFsaXplJywgKCkgPT4ge1xuICAgIGxldCBybmc6ICgpID0+IG51bWJlcjtcbiAgICBsZXQgY2FzZXM6IHN0cmluZ1tdO1xuXG4gICAgLy8gTWF0aC5yYW5kb20oKSBkb2Vzbid0IGFsbG93IHVzIHRvIHNldCBhIHNlZWQsIHNvIHdlIHVzZSBhIGxpYnJhcnkuXG4gICAgYmVmb3JlRWFjaCgoKSA9PiB7XG4gICAgICBybmcgPSBzZWVkcmFuZG9tKCdzb21lIGZpeGVkIHZhbHVlJyk7XG5cbiAgICAgIGZ1bmN0aW9uIF9zdHIobGVuOiBudW1iZXIpIHtcbiAgICAgICAgbGV0IHIgPSAnJztcbiAgICAgICAgY29uc3Qgc3BhY2UgPSAnYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5JztcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW47IGkrKykge1xuICAgICAgICAgIHIgKz0gc3BhY2VbTWF0aC5mbG9vcihybmcoKSAqIHNwYWNlLmxlbmd0aCldO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHI7XG4gICAgICB9XG5cbiAgICAgIC8vIEJ1aWxkIHRlc3QgY2FzZXMuXG4gICAgICBjYXNlcyA9IG5ldyBBcnJheShudW1SYW5kb21JdGVyKVxuICAgICAgICAuZmlsbCgwKVxuICAgICAgICAubWFwKCgpID0+IHtcbiAgICAgICAgICByZXR1cm4gbmV3IEFycmF5KE1hdGguZmxvb3Iocm5nKCkgKiAyMCArIDUpKVxuICAgICAgICAgICAgLmZpbGwoMClcbiAgICAgICAgICAgIC5tYXAoKCkgPT4gX3N0cihybmcoKSAqIDIwICsgMykpXG4gICAgICAgICAgICAuam9pbignLycpO1xuICAgICAgICB9KTtcblxuICAgICAgcmVzZXROb3JtYWxpemVDYWNoZSgpO1xuICAgIH0pO1xuXG4gICAgZGVzY3JpYmUoJ3JhbmRvbSAoMCBjYWNoZSBoaXRzKScsICgpID0+IHtcbiAgICAgIGJlbmNobWFyaygnJywgaSA9PiBub3JtYWxpemUoY2FzZXNbaV0pLCBpID0+IG5vQ2FjaGVOb3JtYWxpemUoY2FzZXNbaV0pKTtcbiAgICB9KTtcblxuICAgIGRlc2NyaWJlKCdyYW5kb20gKDEwJSBjYWNoZSBoaXRzKScsICgpID0+IHtcbiAgICAgIGJlZm9yZUVhY2goKCkgPT4ge1xuICAgICAgICBjYXNlcyA9IGNhc2VzLm1hcCh4ID0+IChybmcoKSA8IDAuMSkgPyBjYXNlc1swXSA6IHgpO1xuICAgICAgfSk7XG4gICAgICBiZW5jaG1hcmsoJycsIGkgPT4gbm9ybWFsaXplKGNhc2VzW2ldKSwgaSA9PiBub0NhY2hlTm9ybWFsaXplKGNhc2VzW2ldKSk7XG4gICAgfSk7XG5cbiAgICBkZXNjcmliZSgncmFuZG9tICgzMCUgY2FjaGUgaGl0cyknLCAoKSA9PiB7XG4gICAgICBiZWZvcmVFYWNoKCgpID0+IHtcbiAgICAgICAgY2FzZXMgPSBjYXNlcy5tYXAoeCA9PiAocm5nKCkgPCAwLjMpID8gY2FzZXNbMF0gOiB4KTtcbiAgICAgIH0pO1xuICAgICAgYmVuY2htYXJrKCcnLCBpID0+IG5vcm1hbGl6ZShjYXNlc1tpXSksIGkgPT4gbm9DYWNoZU5vcm1hbGl6ZShjYXNlc1tpXSkpO1xuICAgIH0pO1xuXG4gICAgZGVzY3JpYmUoJ3JhbmRvbSAoNTAlIGNhY2hlIGhpdHMpJywgKCkgPT4ge1xuICAgICAgYmVmb3JlRWFjaCgoKSA9PiB7XG4gICAgICAgIGNhc2VzID0gY2FzZXMubWFwKHggPT4gKHJuZygpIDwgMC41KSA/IGNhc2VzWzBdIDogeCk7XG4gICAgICB9KTtcbiAgICAgIGJlbmNobWFyaygnJywgaSA9PiBub3JtYWxpemUoY2FzZXNbaV0pLCBpID0+IG5vQ2FjaGVOb3JtYWxpemUoY2FzZXNbaV0pKTtcbiAgICB9KTtcblxuICAgIGRlc2NyaWJlKCdyYW5kb20gKDgwJSBjYWNoZSBoaXRzKScsICgpID0+IHtcbiAgICAgIGJlZm9yZUVhY2goKCkgPT4ge1xuICAgICAgICBjYXNlcyA9IGNhc2VzLm1hcCh4ID0+IChybmcoKSA8IDAuOCkgPyBjYXNlc1swXSA6IHgpO1xuICAgICAgfSk7XG4gICAgICBiZW5jaG1hcmsoJycsIGkgPT4gbm9ybWFsaXplKGNhc2VzW2ldKSwgaSA9PiBub0NhY2hlTm9ybWFsaXplKGNhc2VzW2ldKSk7XG4gICAgfSk7XG4gIH0pO1xufSk7XG4iXX0=