@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Dev\wht-saas\node_modules\.pnpm\@angular-devkit+schematics-cli@0.13.8\node_modules\@angular-devkit\schematics-cli\bin\node_modules;D:\Dev\wht-saas\node_modules\.pnpm\@angular-devkit+schematics-cli@0.13.8\node_modules\@angular-devkit\schematics-cli\node_modules;D:\Dev\wht-saas\node_modules\.pnpm\@angular-devkit+schematics-cli@0.13.8\node_modules\@angular-devkit\node_modules;D:\Dev\wht-saas\node_modules\.pnpm\@angular-devkit+schematics-cli@0.13.8\node_modules;D:\Dev\wht-saas\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Dev\wht-saas\node_modules\.pnpm\@angular-devkit+schematics-cli@0.13.8\node_modules\@angular-devkit\schematics-cli\bin\node_modules;D:\Dev\wht-saas\node_modules\.pnpm\@angular-devkit+schematics-cli@0.13.8\node_modules\@angular-devkit\schematics-cli\node_modules;D:\Dev\wht-saas\node_modules\.pnpm\@angular-devkit+schematics-cli@0.13.8\node_modules\@angular-devkit\node_modules;D:\Dev\wht-saas\node_modules\.pnpm\@angular-devkit+schematics-cli@0.13.8\node_modules;D:\Dev\wht-saas\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\schematics.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\schematics.js" %*
)
