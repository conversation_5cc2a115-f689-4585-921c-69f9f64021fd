"use strict";
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
class BaseException extends Error {
    constructor(message = '') {
        super(message);
    }
}
exports.BaseException = BaseException;
class UnknownException extends BaseException {
    constructor(message) { super(message); }
}
exports.UnknownException = UnknownException;
// Exceptions
class FileDoesNotExistException extends BaseException {
    constructor(path) { super(`Path "${path}" does not exist.`); }
}
exports.FileDoesNotExistException = FileDoesNotExistException;
class FileAlreadyExistException extends BaseException {
    constructor(path) { super(`Path "${path}" already exist.`); }
}
exports.FileAlreadyExistException = FileAlreadyExistException;
class PathIsDirectoryException extends BaseException {
    constructor(path) { super(`Path "${path}" is a directory.`); }
}
exports.PathIsDirectoryException = PathIsDirectoryException;
class PathIsFileException extends BaseException {
    constructor(path) { super(`Path "${path}" is a file.`); }
}
exports.PathIsFileException = PathIsFileException;
class ContentHasMutatedException extends BaseException {
    constructor(path) {
        super(`Content at path "${path}" has changed between the start and the end of an update.`);
    }
}
exports.ContentHasMutatedException = ContentHasMutatedException;
class InvalidUpdateRecordException extends BaseException {
    constructor() { super(`Invalid record instance.`); }
}
exports.InvalidUpdateRecordException = InvalidUpdateRecordException;
class MergeConflictException extends BaseException {
    constructor(path) {
        super(`A merge conflicted on path "${path}".`);
    }
}
exports.MergeConflictException = MergeConflictException;
class UnimplementedException extends BaseException {
    constructor() { super('This function is unimplemented.'); }
}
exports.UnimplementedException = UnimplementedException;
class UnsupportedPlatformException extends BaseException {
    constructor() { super('This platform is not supported by this code path.'); }
}
exports.UnsupportedPlatformException = UnsupportedPlatformException;
//# sourceMappingURL=data:application/json;base64,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