#!/bin/bash

echo "🔄 开始重置数据库..."

# 停止相关服务
echo "⏹️  停止数据库服务..."
docker-compose -f docker-compose.dev.yml stop db

# 删除数据库容器和卷
echo "🗑️  删除数据库容器和卷..."
docker-compose -f docker-compose.dev.yml rm -f db
docker volume rm wht-saas_pg_data 2>/dev/null || true

# 重新构建并启动数据库
echo "🔨 重新构建数据库容器..."
docker-compose -f docker-compose.dev.yml up -d --build db

# 等待数据库启动
echo "⏳ 等待数据库启动..."
attempts=0
max_attempts=30

while [ $attempts -lt $max_attempts ]; do
    if docker-compose -f docker-compose.dev.yml exec -T db pg_isready -U user -d yourdb >/dev/null 2>&1; then
        break
    fi
    
    attempts=$((attempts + 1))
    if [ $attempts -ge $max_attempts ]; then
        echo "❌ 数据库启动超时"
        exit 1
    fi
    
    echo "等待数据库启动... ($attempts/$max_attempts)"
    sleep 2
done

echo "✅ 数据库重置完成!"
echo "📊 数据库状态:"
docker-compose -f docker-compose.dev.yml ps db
