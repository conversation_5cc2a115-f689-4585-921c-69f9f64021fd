#!/bin/bash
set -e

echo "开始执行数据库迁移..."

# 执行迁移文件
for migration in /docker-entrypoint-initdb.d/migrations/*.sql; do
    if [ -f "$migration" ]; then
        echo "执行迁移: $(basename "$migration")"
        psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" -f "$migration"
    fi
done

echo "开始导入种子数据..."

# 执行种子数据
for seeder in /docker-entrypoint-initdb.d/seeders/*.sql; do
    if [ -f "$seeder" ]; then
        echo "导入种子数据: $(basename "$seeder")"
        psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" -f "$seeder"
    fi
done

echo "数据库初始化完成!"
