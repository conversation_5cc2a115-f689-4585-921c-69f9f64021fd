"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const interface_1 = require("../tree/interface");
const static_1 = require("../tree/static");
/**
 * Run a schematic from a separate collection.
 *
 * @param collectionName The name of the collection that contains the schematic to run.
 * @param schematicName The name of the schematic to run.
 * @param options The options to pass as input to the RuleFactory.
 */
function externalSchematic(collectionName, schematicName, options, executionOptions) {
    return (input, context) => {
        const collection = context.engine.createCollection(collectionName);
        const schematic = collection.createSchematic(schematicName);
        return schematic.call(options, rxjs_1.of(static_1.branch(input)), context, executionOptions);
    };
}
exports.externalSchematic = externalSchematic;
/**
 * Run a schematic from the same collection.
 *
 * @param schematicName The name of the schematic to run.
 * @param options The options to pass as input to the RuleFactory.
 */
function schematic(schematicName, options, executionOptions) {
    return (input, context) => {
        const collection = context.schematic.collection;
        const schematic = collection.createSchematic(schematicName, true);
        return schematic.call(options, rxjs_1.of(static_1.branch(input)), context, executionOptions).pipe(operators_1.last(), operators_1.map(x => {
            // We allow overwrite conflict here because they're the only merge conflict we particularly
            // don't want to deal with; the input tree might have an OVERWRITE which the sub
            input.merge(x, interface_1.MergeStrategy.AllowOverwriteConflict);
            return input;
        }));
    };
}
exports.schematic = schematic;
//# sourceMappingURL=data:application/json;base64,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