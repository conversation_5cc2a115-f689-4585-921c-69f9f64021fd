# Auth 模块数据库迁移文件

## 文件说明

### 迁移文件执行顺序
1. `001_create_tenants_table.sql` - 创建租户表
2. `002_create_users_table.sql` - 创建用户表
3. `003_create_roles_table.sql` - 创建角色表
4. `004_create_permissions_table.sql` - 创建权限表
5. `005_create_user_roles_table.sql` - 创建用户-角色关联表
6. `006_create_role_permissions_table.sql` - 创建角色-权限关联表
7. `007_add_users_role_foreign_key.sql` - 添加用户表角色外键
8. `008_create_ip_blacklist_table.sql` - 创建IP黑名单表

## 表结构说明

### 1. tenants (租户表)
- 多租户系统的基础表
- 支持租户状态管理（正常/冻结）
- 组织编码全局唯一

### 2. users (用户表)
- 支持多租户隔离
- 用户名和邮箱在租户内唯一
- 支持账号冻结/解冻逻辑
- 记录登录失败次数和最后登录信息

### 3. roles (角色表)
- 租户级别的角色管理
- 角色编码和名称在租户内唯一

### 4. permissions (权限表)
- 全局权限定义
- 权限编码全局唯一

### 5. user_roles (用户-角色关联表)
- 用户和角色的多对多关系
- 支持一个用户拥有多个角色

### 6. role_permissions (角色-权限关联表)
- 角色和权限的多对多关系
- 支持一个角色拥有多个权限

### 7. ip_blacklist (IP黑名单表)
- IP地址封禁管理
- 支持IPv4和IPv6
- 记录封禁和解禁操作

## 约束说明

### 唯一性约束
- `tenants.org_code` - 全局唯一
- `users(tenant_id, username)` - 租户内用户名唯一
- `users(tenant_id, email)` - 租户内邮箱唯一
- `roles(tenant_id, code)` - 租户内角色编码唯一
- `roles(tenant_id, name)` - 租户内角色名称唯一
- `permissions.code` - 全局权限编码唯一

### 外键约束
- `users.tenant_id` → `tenants.id`
- `users.role_id` → `roles.id`
- `roles.tenant_id` → `tenants.id`
- `user_roles.user_id` → `users.id`
- `user_roles.role_id` → `roles.id`
- `role_permissions.role_id` → `roles.id`
- `role_permissions.permission_id` → `permissions.id`

## 索引说明

### 性能优化索引
- 租户ID相关查询索引
- 用户名、邮箱查询索引
- 角色、权限编码查询索引
- 时间字段索引（创建时间、最后登录时间）

## 触发器说明

### 自动更新时间戳
- 所有主表都有 `updated_at` 字段的自动更新触发器
- 使用统一的 `update_updated_at_column()` 函数
