{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start --port 3000", "lint": "next lint", "dev:webpack": "next dev --port 3000", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3", "jest": "^29.7.0", "@types/jest": "^29.5.12", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.1.0"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "testPathIgnorePatterns": ["<rootDir>/.next/", "<rootDir>/node_modules/"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/**/index.{js,ts}"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}