import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  HttpStatus
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth
} from '@nestjs/swagger';
import { CreateTaskDto, UpdateTaskDto, TaskResponseDto, TaskListResponseDto } from '../dto/task.dto';
import { ApiResponseDto, ErrorResponseDto } from '../dto/common.dto';

@ApiTags('Tasks')
@ApiBearerAuth()
@Controller('tasks')
export class TasksController {

  @Get()
  @ApiOperation({
    summary: '获取任务列表',
    description: '分页获取任务列表，支持状态筛选'
  })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量', example: 10 })
  @ApiQuery({ name: 'status', required: false, description: '任务状态筛选' })
  @ApiResponse({
    status: 200,
    description: '获取任务列表成功',
    type: TaskListResponseDto
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
    type: ErrorResponseDto
  })
  getTasks(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('status') status?: string,
  ): TaskListResponseDto {
    // 临时返回示例数据
    return {
      data: [
        {
          id: 1,
          title: '示例任务',
          description: '这是一个示例任务',
          status: 'pending' as any,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      ],
      total: 1,
      page: Number(page),
      limit: Number(limit),
    };
  }

  @Get(':id')
  @ApiOperation({
    summary: '获取任务详情',
    description: '根据任务ID获取任务详细信息'
  })
  @ApiParam({ name: 'id', description: '任务ID', example: 1 })
  @ApiResponse({
    status: 200,
    description: '获取任务详情成功',
    type: TaskResponseDto
  })
  @ApiResponse({
    status: 404,
    description: '任务不存在',
    type: ErrorResponseDto
  })
  getTask(@Param('id', ParseIntPipe) id: number): TaskResponseDto {
    // 临时返回示例数据
    return {
      id,
      title: '示例任务',
      description: '这是一个示例任务',
      status: 'pending' as any,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  @Post()
  @ApiOperation({
    summary: '创建任务',
    description: '创建一个新的任务'
  })
  @ApiResponse({
    status: 201,
    description: '任务创建成功',
    type: TaskResponseDto
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
    type: ErrorResponseDto
  })
  createTask(@Body() createTaskDto: CreateTaskDto): TaskResponseDto {
    // 临时返回示例数据
    return {
      id: 1,
      title: createTaskDto.title,
      description: createTaskDto.description || '',
      status: createTaskDto.status || 'pending' as any,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  @Put(':id')
  @ApiOperation({
    summary: '更新任务',
    description: '根据任务ID更新任务信息'
  })
  @ApiParam({ name: 'id', description: '任务ID', example: 1 })
  @ApiResponse({
    status: 200,
    description: '任务更新成功',
    type: TaskResponseDto
  })
  @ApiResponse({
    status: 404,
    description: '任务不存在',
    type: ErrorResponseDto
  })
  updateTask(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateTaskDto: UpdateTaskDto,
  ): TaskResponseDto {
    // 临时返回示例数据
    return {
      id,
      title: updateTaskDto.title || '示例任务',
      description: updateTaskDto.description || '这是一个示例任务',
      status: updateTaskDto.status || 'pending' as any,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  @Delete(':id')
  @ApiOperation({
    summary: '删除任务',
    description: '根据任务ID删除任务'
  })
  @ApiParam({ name: 'id', description: '任务ID', example: 1 })
  @ApiResponse({
    status: 200,
    description: '任务删除成功',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: '任务删除成功' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '任务不存在',
    type: ErrorResponseDto
  })
  deleteTask(@Param('id', ParseIntPipe) id: number): { message: string } {
    return { message: '任务删除成功' };
  }
}
