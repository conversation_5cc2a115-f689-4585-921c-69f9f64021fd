-- IP黑名单示例数据
-- 环境: 开发环境
-- 描述: 创建IP黑名单的测试数据

-- IP黑名单示例数据
INSERT INTO ip_blacklist (ip_address, reason, status, banned_at, operator_id, created_at, updated_at) VALUES 
('*************', '恶意攻击，多次尝试暴力破解', 1, CURRENT_TIMESTAMP - INTERVAL '1 day', 1, CURRENT_TIMESTAMP - INTERVAL '1 day', CURRENT_TIMESTAMP - INTERVAL '1 day'),
('*********', '异常登录行为，疑似机器人', 1, CURRENT_TIMESTAMP - INTERVAL '2 hours', 1, CURRENT_TIMESTAMP - INTERVAL '2 hours', CURRENT_TIMESTAMP - INTERVAL '2 hours'),
('***********', '违规操作，已手动解禁', 2, CURRENT_TIMESTAMP - INTERVAL '7 days', 1, CURRENT_TIMESTAMP - INTERVAL '7 days', CURRENT_TIMESTAMP - INTERVAL '1 day')
ON CONFLICT DO NOTHING;

-- 更新解禁记录的解禁时间
UPDATE ip_blacklist 
SET unbanned_at = CURRENT_TIMESTAMP - INTERVAL '1 day', 
    updated_at = CURRENT_TIMESTAMP - INTERVAL '1 day'
WHERE ip_address = '***********' AND status = 2;




