-- 风控与安全表的示例数据
-- 环境: 开发环境
-- 描述: 创建风控和安全相关的测试数据

-- IP黑名单示例数据
INSERT INTO ip_blacklist (ip_address, reason, status, banned_at, operator_id, created_at, updated_at) VALUES 
('*************', '恶意攻击，多次尝试暴力破解', 1, CURRENT_TIMESTAMP - INTERVAL '1 day', 1, CURRENT_TIMESTAMP - INTERVAL '1 day', CURRENT_TIMESTAMP - INTERVAL '1 day'),
('*********', '异常登录行为，疑似机器人', 1, CURRENT_TIMESTAMP - INTERVAL '2 hours', 1, CURRENT_TIMESTAMP - INTERVAL '2 hours', CURRENT_TIMESTAMP - INTERVAL '2 hours'),
('***********', '违规操作，已手动解禁', 2, CURRENT_TIMESTAMP - INTERVAL '7 days', 1, CURRENT_TIMESTAMP - INTERVAL '7 days', CURRENT_TIMESTAMP - INTERVAL '1 day')
ON CONFLICT DO NOTHING;

-- 更新解禁记录的解禁时间
UPDATE ip_blacklist 
SET unbanned_at = CURRENT_TIMESTAMP - INTERVAL '1 day', 
    updated_at = CURRENT_TIMESTAMP - INTERVAL '1 day'
WHERE ip_address = '***********' AND status = 2;

-- JWT黑名单示例数据
INSERT INTO jwt_blacklist (jti, user_id, reason, expired_at, created_at) VALUES 
('jwt_test_001_' || extract(epoch from now())::text, 2, '用户主动登出', CURRENT_TIMESTAMP + INTERVAL '1 hour', CURRENT_TIMESTAMP - INTERVAL '30 minutes'),
('jwt_test_002_' || extract(epoch from now())::text, 3, '检测到异常登录，强制下线', CURRENT_TIMESTAMP + INTERVAL '2 hours', CURRENT_TIMESTAMP - INTERVAL '15 minutes'),
('jwt_test_003_' || extract(epoch from now())::text, 2, '密码修改，撤销所有令牌', CURRENT_TIMESTAMP + INTERVAL '30 minutes', CURRENT_TIMESTAMP - INTERVAL '5 minutes')
ON CONFLICT (jti) DO NOTHING;

-- 用户冻结日志示例数据
INSERT INTO user_lock_log (user_id, ip_address, lock_type, reason, locked_at, unlock_at, operator_id, status, created_at, updated_at) VALUES 
-- 系统自动冻结（登录失败次数过多）
(3, '*************', 'account', '连续登录失败5次，系统自动冻结', CURRENT_TIMESTAMP - INTERVAL '3 hours', CURRENT_TIMESTAMP - INTERVAL '2 hours', NULL, 2, CURRENT_TIMESTAMP - INTERVAL '3 hours', CURRENT_TIMESTAMP - INTERVAL '2 hours'),

-- 管理员手动冻结
(2, '**********', 'account', '违规操作，管理员手动冻结', CURRENT_TIMESTAMP - INTERVAL '1 day', NULL, 1, 1, CURRENT_TIMESTAMP - INTERVAL '1 day', CURRENT_TIMESTAMP - INTERVAL '1 day'),

-- IP冻结记录
(3, '***********', 'ip', '来自可疑IP的登录尝试', CURRENT_TIMESTAMP - INTERVAL '6 hours', CURRENT_TIMESTAMP - INTERVAL '4 hours', NULL, 2, CURRENT_TIMESTAMP - INTERVAL '6 hours', CURRENT_TIMESTAMP - INTERVAL '4 hours')
ON CONFLICT DO NOTHING;
