# PowerShell 数据库重置脚本

Write-Host "🔄 开始重置数据库..." -ForegroundColor Green

try {
    # 停止相关服务
    Write-Host "⏹️  停止数据库服务..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml stop db
    
    # 删除数据库容器和卷
    Write-Host "🗑️  删除数据库容器和卷..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml rm -f db
    docker volume rm wht-saas_pg_data 2>$null
    
    # 重新构建并启动数据库
    Write-Host "🔨 重新构建数据库容器..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml up -d --build db
    
    # 等待数据库启动
    Write-Host "⏳ 等待数据库启动..." -ForegroundColor Yellow
    $attempts = 0
    $maxAttempts = 30
    $dbReady = $false
    
    while ($attempts -lt $maxAttempts -and -not $dbReady) {
        try {
            docker-compose -f docker-compose.dev.yml exec -T db pg_isready -U user -d yourdb 2>$null
            if ($LASTEXITCODE -eq 0) {
                $dbReady = $true
            }
        }
        catch {
            # 继续等待
        }
        
        if (-not $dbReady) {
            $attempts++
            if ($attempts -ge $maxAttempts) {
                throw "数据库启动超时"
            }
            Write-Host "等待数据库启动... ($attempts/$maxAttempts)" -ForegroundColor Cyan
            Start-Sleep -Seconds 2
        }
    }
    
    Write-Host "✅ 数据库重置完成!" -ForegroundColor Green
    Write-Host "📊 数据库状态:" -ForegroundColor Cyan
    docker-compose -f docker-compose.dev.yml ps db
    
} catch {
    Write-Host "❌ 数据库重置失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
