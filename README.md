# WHT SaaS Platform

## 项目结构
```
wht-saas/
├── apps/
│   ├── frontend/          # Next.js 前端应用 (端口: 3000)
│   └── backend/           # NestJS 后端应用 (端口: 4000)
├── database/              # 数据库配置和脚本
│   ├── migrations/        # 数据库迁移文件
│   ├── seeders/          # 种子数据
│   ├── scripts/          # 管理脚本
│   └── Dockerfile        # 数据库镜像
├── docker-compose.yml     # 生产环境配置
├── docker-compose.dev.yml # 开发环境配置
└── sonar-project.properties # SonarQube 配置
```

## Docker 开发环境

### 启动开发环境
```bash
# 启动完整开发环境 (前端 + 后端 + 数据库)
docker-compose -f docker-compose.dev.yml up --build

# 后台启动
docker-compose -f docker-compose.dev.yml up -d --build

# 只启动特定服务
docker-compose -f docker-compose.dev.yml up frontend
docker-compose -f docker-compose.dev.yml up backend
docker-compose -f docker-compose.dev.yml up db
```

### 停止和清理
```bash
# 停止服务
docker-compose -f docker-compose.dev.yml down

# 停止并删除卷
docker-compose -f docker-compose.dev.yml down -v

# 重建容器
docker-compose -f docker-compose.dev.yml up --build --force-recreate
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.dev.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.dev.yml logs -f frontend
docker-compose -f docker-compose.dev.yml logs -f backend
```

### 进入容器
```bash
# 进入前端容器
docker-compose -f docker-compose.dev.yml exec frontend bash

# 进入后端容器
docker-compose -f docker-compose.dev.yml exec backend bash

# 进入数据库容器
docker-compose -f docker-compose.dev.yml exec db psql -U user -d yourdb
```

## 本地开发

### 安装依赖
```bash
# 安装所有依赖
pnpm install

# 只安装前端依赖
pnpm --filter frontend install

# 只安装后端依赖
pnpm --filter backend install
```

### 启动服务
```bash
# 同时启动前后端
pnpm dev

# 只启动前端 (webpack 方式)
pnpm dev:frontend

# 只启动后端
pnpm dev:backend
```

## SonarQube 代码质量检查

### 启动 SonarQube
```bash
# 启动 SonarQube 服务
docker-compose -f docker-compose.dev.yml up -d sonarqube db
```

### 访问 SonarQube
- 地址: http://localhost:9000
- 默认用户名: admin
- 默认密码: admin

### 首次配置
1. 访问 http://localhost:9000
2. 使用 admin/admin 登录
3. 修改默认密码
4. 创建项目 token: Administration → Security → Users → Tokens

### 运行代码扫描
```bash
# 生成测试覆盖率报告
pnpm --filter frontend run test:cov
pnpm --filter backend run test:cov

# 运行 SonarQube 扫描
npx sonar-scanner

# 或使用项目脚本
pnpm run sonar:scan
```

### 质量门规则
- 代码覆盖率 ≥ 80%
- 重复代码率 ≤ 3%
- 可维护性评级 = A
- 可靠性评级 = A
- 安全性评级 = A

## Jest 测试

### 前端测试
```bash
# 进入前端目录
cd apps/frontend

# 运行测试
pnpm test

# 监听模式
pnpm test:watch

# 生成覆盖率报告
pnpm test:cov
```

### 后端测试
```bash
# 进入后端目录
cd apps/backend

# 运行单元测试
pnpm test

# 运行 E2E 测试
pnpm test:e2e

# 生成覆盖率报告
pnpm test:cov

# 监听模式
pnpm test:watch
```

### 测试文件位置
- 前端: `apps/frontend/src/**/*.{test,spec}.{js,ts,tsx}`
- 后端: `apps/backend/src/**/*.spec.ts`, `apps/backend/test/**/*.spec.ts`

## ESLint 代码检查

### 前端 ESLint
```bash
# 进入前端目录
cd apps/frontend

# 检查代码
pnpm lint

# 自动修复
pnpm lint --fix
```

### 后端 ESLint
```bash
# 进入后端目录
cd apps/backend

# 检查代码
pnpm lint
```

### 配置文件
- 前端: `apps/frontend/eslint.config.mjs`
- 后端: `apps/backend/tslint.json`

## Prettier 代码格式化

### 前端格式化
```bash
# 进入前端目录
cd apps/frontend

# 检查格式
npx prettier --check "src/**/*.{js,ts,tsx}"

# 格式化代码
npx prettier --write "src/**/*.{js,ts,tsx}"
```

### 后端格式化
```bash
# 进入后端目录
cd apps/backend

# 格式化代码
pnpm format
```

### 配置文件
在项目根目录创建 `.prettierrc` 文件来统一格式化规则:
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

## 环境变量

### 开发环境
- 前端: `apps/frontend/.env.local`
- 后端: `apps/backend/.env`
- 根目录: `.env.development`

### 端口配置
- 前端: 3000
- 后端: 4000 (API: /api, Swagger: /api/docs)
- 数据库: 5432
- SonarQube: 9000

## Swagger API 文档

### 访问地址
- Swagger UI: http://localhost:4000/api/docs
- OpenAPI JSON: http://localhost:4000/api/docs-json
- OpenAPI YAML: http://localhost:4000/api/docs-yaml

### 可用接口
```bash
# 健康检查
GET /api/hc

# 任务管理
GET /api/tasks          # 获取任务列表
GET /api/tasks/:id      # 获取任务详情
POST /api/tasks         # 创建任务
PUT /api/tasks/:id      # 更新任务
DELETE /api/tasks/:id   # 删除任务
```

## 数据库管理

### 一键重置数据库
```bash
# 完全重置数据库
pnpm run db:reset

# 重置数据库并启动开发环境
pnpm run dev:clean
```

### 数据库操作
```bash
# 启动数据库
pnpm run db:start

# 停止数据库
pnpm run db:stop

# 查看数据库日志
pnpm run db:logs
```

### 数据库文件结构
- `database/migrations/` - 数据库迁移文件
- `database/seeders/` - 种子数据文件
- `database/schema/` - 数据库结构定义
- `database/scripts/` - 管理脚本

## 常见问题

### 端口被占用
```bash
# Windows
netstat -ano | findstr ":3000"
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:3000 | xargs kill -9
```

### Docker 容器问题
```bash
# 清理所有容器和镜像
docker system prune -a

# 重建特定服务
docker-compose -f docker-compose.dev.yml up --build --force-recreate frontend
```
