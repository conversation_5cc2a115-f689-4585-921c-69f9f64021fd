# Auth 模块种子数据

## 文件说明

### 种子数据执行顺序
1. `001_default_tenant.sql` - 默认租户数据
2. `002_default_permissions.sql` - 默认权限数据
3. `003_default_roles.sql` - 默认角色数据
4. `004_default_role_permissions.sql` - 角色权限关联数据
5. `005_default_users.sql` - 默认用户数据

## 默认数据说明

### 默认租户
- **租户名**: 默认租户
- **组织编码**: DEFAULT_ORG
- **状态**: 正常

### 默认权限
包含以下权限模块：
- **用户管理**: user:read, user:create, user:update, user:delete
- **角色管理**: role:read, role:create, role:update, role:delete
- **权限管理**: permission:read, permission:assign
- **租户管理**: tenant:read, tenant:update
- **系统管理**: system:config, system:logs
- **任务管理**: task:read, task:create, task:update, task:delete

### 默认角色
1. **超级管理员** (super_admin)
   - 拥有所有权限
   
2. **管理员** (admin)
   - 用户、角色、权限、租户、任务管理权限
   - 系统日志查看权限
   - 不包含系统配置权限

3. **普通用户** (user)
   - 基础查看权限
   - 任务的查看、创建、编辑权限

### 默认用户
1. **admin** (<EMAIL>)
   - 角色: 超级管理员
   - 密码: password123

2. **manager** (<EMAIL>)
   - 角色: 管理员
   - 密码: password123

3. **user** (<EMAIL>)
   - 角色: 普通用户
   - 密码: password123

## 安全说明

⚠️ **重要**: 默认用户的密码都是 `password123`，这仅用于开发和测试环境。

在生产环境中请务必：
1. 修改所有默认用户的密码
2. 删除不需要的测试用户
3. 使用强密码策略
4. 启用多因素认证

## 使用方法

这些种子数据会在数据库初始化时自动执行，为系统提供基础的认证授权数据。

开发者可以使用默认用户进行登录测试：
- 超级管理员测试: admin / password123
- 管理员测试: manager / password123  
- 普通用户测试: user / password123
