"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const core_1 = require("@angular-devkit/core");
const operators_1 = require("rxjs/operators");
function formatValidator(data, dataSchema, formats) {
    const registry = new core_1.schema.CoreSchemaRegistry();
    for (const format of formats) {
        registry.addFormat(format);
    }
    return registry
        .compile(dataSchema)
        .pipe(operators_1.mergeMap(validator => validator(data)));
}
exports.formatValidator = formatValidator;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZm9ybWF0LXZhbGlkYXRvci5qcyIsInNvdXJjZVJvb3QiOiIuLyIsInNvdXJjZXMiOlsicGFja2FnZXMvYW5ndWxhcl9kZXZraXQvc2NoZW1hdGljcy9zcmMvZm9ybWF0cy9mb3JtYXQtdmFsaWRhdG9yLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBQUE7Ozs7OztHQU1HO0FBQ0gsK0NBQXFFO0FBRXJFLDhDQUEwQztBQUcxQyxTQUFnQixlQUFlLENBQzdCLElBQWUsRUFDZixVQUFzQixFQUN0QixPQUE4QjtJQUU5QixNQUFNLFFBQVEsR0FBRyxJQUFJLGFBQU0sQ0FBQyxrQkFBa0IsRUFBRSxDQUFDO0lBRWpELEtBQUssTUFBTSxNQUFNLElBQUksT0FBTyxFQUFFO1FBQzVCLFFBQVEsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUM7S0FDNUI7SUFFRCxPQUFPLFFBQVE7U0FDWixPQUFPLENBQUMsVUFBVSxDQUFDO1NBQ25CLElBQUksQ0FBQyxvQkFBUSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztBQUNsRCxDQUFDO0FBZEQsMENBY0MiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIEluYy4gQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5pbXBvcnQgeyBKc29uT2JqZWN0LCBKc29uVmFsdWUsIHNjaGVtYSB9IGZyb20gJ0Bhbmd1bGFyLWRldmtpdC9jb3JlJztcbmltcG9ydCB7IE9ic2VydmFibGUgfSBmcm9tICdyeGpzJztcbmltcG9ydCB7IG1lcmdlTWFwIH0gZnJvbSAncnhqcy9vcGVyYXRvcnMnO1xuXG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRWYWxpZGF0b3IoXG4gIGRhdGE6IEpzb25WYWx1ZSxcbiAgZGF0YVNjaGVtYTogSnNvbk9iamVjdCxcbiAgZm9ybWF0czogc2NoZW1hLlNjaGVtYUZvcm1hdFtdLFxuKTogT2JzZXJ2YWJsZTxzY2hlbWEuU2NoZW1hVmFsaWRhdG9yUmVzdWx0PiB7XG4gIGNvbnN0IHJlZ2lzdHJ5ID0gbmV3IHNjaGVtYS5Db3JlU2NoZW1hUmVnaXN0cnkoKTtcblxuICBmb3IgKGNvbnN0IGZvcm1hdCBvZiBmb3JtYXRzKSB7XG4gICAgcmVnaXN0cnkuYWRkRm9ybWF0KGZvcm1hdCk7XG4gIH1cblxuICByZXR1cm4gcmVnaXN0cnlcbiAgICAuY29tcGlsZShkYXRhU2NoZW1hKVxuICAgIC5waXBlKG1lcmdlTWFwKHZhbGlkYXRvciA9PiB2YWxpZGF0b3IoZGF0YSkpKTtcbn1cbiJdfQ==