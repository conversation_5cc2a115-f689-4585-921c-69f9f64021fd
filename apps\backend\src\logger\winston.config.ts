import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';

// ELK 专用格式化器
const elkFormatter = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DDTHH:mm:ss.SSSZ' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    const { timestamp, level, message, traceId, correlationId, userId, method, url, statusCode, responseTime, userAgent, ip, stack, ...metadata } = info;
    return JSON.stringify({
      '@timestamp': timestamp,
      level,
      message,
      service: 'wht-saas-backend',
      environment: process.env.NODE_ENV || 'development',
      traceId: traceId || null,
      correlationId: correlationId || null,
      userId: userId || null,
      method: method || null,
      url: url || null,
      statusCode: statusCode || null,
      responseTime: responseTime || null,
      userAgent: userAgent || null,
      ip: ip || null,
      stack: stack || null,
      ...metadata,
    });
  }),
);

// 控制台格式化器（开发环境）
const consoleFormatter = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize(),
  winston.format.printf((info) => {
    const { timestamp, level, message, traceId, ...meta } = info;
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    const traceStr = traceId ? `[${traceId}]` : '';
    return `${timestamp} ${level} ${traceStr}: ${message} ${metaStr}`;
  }),
);

// 创建传输器
const createTransports = () => {
  const transports: winston.transport[] = [];

  // 控制台传输器
  if (process.env.NODE_ENV !== 'production') {
    transports.push(
      new winston.transports.Console({
        format: consoleFormatter,
        level: 'debug',
      }),
    );
  }

  // 文件传输器 - 所有日志
  transports.push(
    new DailyRotateFile({
      filename: 'logs/app-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d',
      format: elkFormatter,
      level: 'info',
    }),
  );

  // 文件传输器 - 错误日志
  transports.push(
    new DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '30d',
      format: elkFormatter,
      level: 'error',
    }),
  );

  // 访问日志传输器
  transports.push(
    new DailyRotateFile({
      filename: 'logs/access-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '7d',
      format: elkFormatter,
      level: 'info',
    }),
  );

  return transports;
};

// Winston 配置
export const winstonConfig: winston.LoggerOptions = {
  level: process.env.LOG_LEVEL || 'info',
  format: elkFormatter,
  transports: createTransports(),
  exitOnError: false,
};

// 创建 logger 实例
export const logger = winston.createLogger(winstonConfig);
