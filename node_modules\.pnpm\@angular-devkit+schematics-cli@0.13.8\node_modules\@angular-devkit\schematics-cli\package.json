{"name": "@angular-devkit/schematics-cli", "version": "0.13.8", "description": "Angular Schematics - CLI", "bin": {"schematics": "./bin/schematics.js"}, "keywords": ["angular", "Angular CLI", "devkit", "sdk", "Angular DevKit", "blueprints", "scaffolding", "template", "tooling", "code generation", "schematics"], "dependencies": {"@angular-devkit/core": "7.3.8", "@angular-devkit/schematics": "7.3.8", "@schematics/schematics": "0.13.8", "inquirer": "6.2.1", "minimist": "1.2.0", "rxjs": "6.3.3", "symbol-observable": "1.2.0"}, "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "engines": {"node": ">= 8.9.0", "npm": ">= 5.5.1"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli"}