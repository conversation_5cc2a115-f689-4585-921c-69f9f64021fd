"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const rxjs_1 = require("rxjs");
const json_1 = require("../../json");
const api_1 = require("./api");
const exception_1 = require("./exception");
/**
 * A simple job registry that keep a map of JobName => JobHandler internally.
 */
class SimpleJobRegistry {
    constructor() {
        this._jobNames = new Map();
    }
    get(name) {
        return rxjs_1.of(this._jobNames.get(name) || null);
    }
    register(nameOrHandler, handlerOrOptions = {}, options = {}) {
        // Switch on the arguments.
        if (typeof nameOrHandler == 'string') {
            if (!api_1.isJobHandler(handlerOrOptions)) {
                // This is an error.
                throw new TypeError('Expected a JobHandler as second argument.');
            }
            this._register(nameOrHandler, handlerOrOptions, options);
        }
        else if (api_1.isJobHandler(nameOrHandler)) {
            if (typeof handlerOrOptions !== 'object') {
                // This is an error.
                throw new TypeError('Expected an object options as second argument.');
            }
            const name = options.name || nameOrHandler.jobDescription.name || handlerOrOptions.name;
            if (name === undefined) {
                throw new TypeError('Expected name to be a string.');
            }
            this._register(name, nameOrHandler, options);
        }
        else {
            throw new TypeError('Unrecognized arguments.');
        }
    }
    _register(name, handler, options) {
        if (this._jobNames.has(name)) {
            // We shouldn't allow conflicts.
            throw new exception_1.JobNameAlreadyRegisteredException(name);
        }
        // Merge all fields with the ones in the handler (to make sure we respect the handler).
        const argument = json_1.schema.mergeSchemas(handler.jobDescription.argument, options.argument);
        const input = json_1.schema.mergeSchemas(handler.jobDescription.input, options.input);
        const output = json_1.schema.mergeSchemas(handler.jobDescription.output, options.output);
        // Create the job description.
        const jobDescription = {
            name,
            argument,
            output,
            input,
        };
        this._jobNames.set(name, Object.assign(handler.bind(undefined), { jobDescription }));
    }
    /**
     * Returns the job names of all jobs.
     */
    getJobNames() {
        return [...this._jobNames.keys()];
    }
}
exports.SimpleJobRegistry = SimpleJobRegistry;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2ltcGxlLXJlZ2lzdHJ5LmpzIiwic291cmNlUm9vdCI6Ii4vIiwic291cmNlcyI6WyJwYWNrYWdlcy9hbmd1bGFyX2RldmtpdC9jb3JlL3NyYy9leHBlcmltZW50YWwvam9icy9zaW1wbGUtcmVnaXN0cnkudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7QUFBQTs7Ozs7O0dBTUc7QUFDSCwrQkFBc0M7QUFDdEMscUNBQStDO0FBQy9DLCtCQUFvRjtBQUNwRiwyQ0FBZ0U7QUFRaEU7O0dBRUc7QUFDSCxNQUFhLGlCQUFpQjtJQUE5QjtRQUtVLGNBQVMsR0FBRyxJQUFJLEdBQUcsRUFHeEIsQ0FBQztJQWtITixDQUFDO0lBaEhDLEdBQUcsQ0FJRCxJQUFhO1FBQ2IsT0FBTyxTQUFFLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFpQyxJQUFJLElBQUksQ0FBQyxDQUFDO0lBQzlFLENBQUM7SUErQkQsUUFBUSxDQUNOLGFBQStELEVBQy9ELG1CQUFnRixFQUFFLEVBQ2xGLFVBQThCLEVBQUU7UUFFaEMsMkJBQTJCO1FBQzNCLElBQUksT0FBTyxhQUFhLElBQUksUUFBUSxFQUFFO1lBQ3BDLElBQUksQ0FBQyxrQkFBWSxDQUFDLGdCQUFnQixDQUFDLEVBQUU7Z0JBQ25DLG9CQUFvQjtnQkFDcEIsTUFBTSxJQUFJLFNBQVMsQ0FBQywyQ0FBMkMsQ0FBQyxDQUFDO2FBQ2xFO1lBRUQsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLEVBQUUsZ0JBQWdCLEVBQUUsT0FBTyxDQUFDLENBQUM7U0FDMUQ7YUFBTSxJQUFJLGtCQUFZLENBQUMsYUFBYSxDQUFDLEVBQUU7WUFDdEMsSUFBSSxPQUFPLGdCQUFnQixLQUFLLFFBQVEsRUFBRTtnQkFDeEMsb0JBQW9CO2dCQUNwQixNQUFNLElBQUksU0FBUyxDQUFDLGdEQUFnRCxDQUFDLENBQUM7YUFDdkU7WUFFRCxNQUFNLElBQUksR0FBRyxPQUFPLENBQUMsSUFBSSxJQUFJLGFBQWEsQ0FBQyxjQUFjLENBQUMsSUFBSSxJQUFJLGdCQUFnQixDQUFDLElBQUksQ0FBQztZQUN4RixJQUFJLElBQUksS0FBSyxTQUFTLEVBQUU7Z0JBQ3RCLE1BQU0sSUFBSSxTQUFTLENBQUMsK0JBQStCLENBQUMsQ0FBQzthQUN0RDtZQUVELElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxFQUFFLGFBQWEsRUFBRSxPQUFPLENBQUMsQ0FBQztTQUM5QzthQUFNO1lBQ0wsTUFBTSxJQUFJLFNBQVMsQ0FBQyx5QkFBeUIsQ0FBQyxDQUFDO1NBQ2hEO0lBQ0gsQ0FBQztJQUVTLFNBQVMsQ0FLakIsSUFBYSxFQUNiLE9BQStDLEVBQy9DLE9BQTJCO1FBRTNCLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDNUIsZ0NBQWdDO1lBQ2hDLE1BQU0sSUFBSSw2Q0FBaUMsQ0FBQyxJQUFJLENBQUMsQ0FBQztTQUNuRDtRQUVELHVGQUF1RjtRQUN2RixNQUFNLFFBQVEsR0FBRyxhQUFNLENBQUMsWUFBWSxDQUNsQyxPQUFPLENBQUMsY0FBYyxDQUFDLFFBQVEsRUFDL0IsT0FBTyxDQUFDLFFBQVEsQ0FDakIsQ0FBQztRQUNGLE1BQU0sS0FBSyxHQUFHLGFBQU0sQ0FBQyxZQUFZLENBQy9CLE9BQU8sQ0FBQyxjQUFjLENBQUMsS0FBSyxFQUM1QixPQUFPLENBQUMsS0FBSyxDQUNkLENBQUM7UUFDRixNQUFNLE1BQU0sR0FBRyxhQUFNLENBQUMsWUFBWSxDQUNoQyxPQUFPLENBQUMsY0FBYyxDQUFDLE1BQU0sRUFDN0IsT0FBTyxDQUFDLE1BQU0sQ0FDZixDQUFDO1FBRUYsOEJBQThCO1FBQzlCLE1BQU0sY0FBYyxHQUFtQjtZQUNyQyxJQUFJO1lBQ0osUUFBUTtZQUNSLE1BQU07WUFDTixLQUFLO1NBQ04sQ0FBQztRQUVGLElBQUksQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLElBQUksRUFBRSxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEVBQUUsRUFBRSxjQUFjLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFDdkYsQ0FBQztJQUVEOztPQUVHO0lBQ0gsV0FBVztRQUNULE9BQU8sQ0FBQyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQztJQUNwQyxDQUFDO0NBQ0Y7QUExSEQsOENBMEhDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBJbmMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuaW1wb3J0IHsgT2JzZXJ2YWJsZSwgb2YgfSBmcm9tICdyeGpzJztcbmltcG9ydCB7IEpzb25WYWx1ZSwgc2NoZW1hIH0gZnJvbSAnLi4vLi4vanNvbic7XG5pbXBvcnQgeyBKb2JEZXNjcmlwdGlvbiwgSm9iSGFuZGxlciwgSm9iTmFtZSwgUmVnaXN0cnksIGlzSm9iSGFuZGxlciB9IGZyb20gJy4vYXBpJztcbmltcG9ydCB7IEpvYk5hbWVBbHJlYWR5UmVnaXN0ZXJlZEV4Y2VwdGlvbiB9IGZyb20gJy4vZXhjZXB0aW9uJztcblxuXG4vKipcbiAqIFNpbXBsZUpvYlJlZ2lzdHJ5IGpvYiByZWdpc3RyYXRpb24gb3B0aW9ucy5cbiAqL1xuZXhwb3J0IGludGVyZmFjZSBSZWdpc3RlckpvYk9wdGlvbnMgZXh0ZW5kcyBQYXJ0aWFsPEpvYkRlc2NyaXB0aW9uPiB7fVxuXG4vKipcbiAqIEEgc2ltcGxlIGpvYiByZWdpc3RyeSB0aGF0IGtlZXAgYSBtYXAgb2YgSm9iTmFtZSA9PiBKb2JIYW5kbGVyIGludGVybmFsbHkuXG4gKi9cbmV4cG9ydCBjbGFzcyBTaW1wbGVKb2JSZWdpc3RyeTxcbiAgTWluaW11bUFyZ3VtZW50VmFsdWVUIGV4dGVuZHMgSnNvblZhbHVlID0gSnNvblZhbHVlLFxuICBNaW5pbXVtSW5wdXRWYWx1ZVQgZXh0ZW5kcyBKc29uVmFsdWUgPSBKc29uVmFsdWUsXG4gIE1pbmltdW1PdXRwdXRWYWx1ZVQgZXh0ZW5kcyBKc29uVmFsdWUgPSBKc29uVmFsdWUsXG4+IGltcGxlbWVudHMgUmVnaXN0cnk8TWluaW11bUFyZ3VtZW50VmFsdWVULCBNaW5pbXVtSW5wdXRWYWx1ZVQsIE1pbmltdW1PdXRwdXRWYWx1ZVQ+IHtcbiAgcHJpdmF0ZSBfam9iTmFtZXMgPSBuZXcgTWFwPFxuICAgIEpvYk5hbWUsXG4gICAgSm9iSGFuZGxlcjxNaW5pbXVtQXJndW1lbnRWYWx1ZVQsIE1pbmltdW1JbnB1dFZhbHVlVCwgTWluaW11bU91dHB1dFZhbHVlVD5cbiAgPigpO1xuXG4gIGdldDxcbiAgICBBIGV4dGVuZHMgTWluaW11bUFyZ3VtZW50VmFsdWVUID0gTWluaW11bUFyZ3VtZW50VmFsdWVULFxuICAgIEkgZXh0ZW5kcyBNaW5pbXVtSW5wdXRWYWx1ZVQgPSBNaW5pbXVtSW5wdXRWYWx1ZVQsXG4gICAgTyBleHRlbmRzIE1pbmltdW1PdXRwdXRWYWx1ZVQgPSBNaW5pbXVtT3V0cHV0VmFsdWVULFxuICA+KG5hbWU6IEpvYk5hbWUpOiBPYnNlcnZhYmxlPEpvYkhhbmRsZXI8QSwgSSwgTz4gfCBudWxsPiB7XG4gICAgcmV0dXJuIG9mKHRoaXMuX2pvYk5hbWVzLmdldChuYW1lKSBhcyAoSm9iSGFuZGxlcjxBLCBJLCBPPiB8IG51bGwpIHx8IG51bGwpO1xuICB9XG5cbiAgLyoqXG4gICAqIFJlZ2lzdGVyIGEgam9iIGhhbmRsZXIuIFRoZSBuYW1lIG11c3QgYmUgdW5pcXVlLlxuICAgKlxuICAgKiBAcGFyYW0gbmFtZSBUaGUgbmFtZSBvZiB0aGUgam9iLlxuICAgKiBAcGFyYW0gaGFuZGxlciBUaGUgZnVuY3Rpb24gdGhhdCB3aWxsIGJlIGNhbGxlZCBmb3IgdGhlIGpvYi5cbiAgICogQHBhcmFtIG9wdGlvbnMgQW4gb3B0aW9uYWwgbGlzdCBvZiBvcHRpb25zIHRvIG92ZXJyaWRlIHRoZSBoYW5kbGVyLiB7QHNlZSBSZWdpc3RlckpvYk9wdGlvbnN9XG4gICAqL1xuICByZWdpc3RlcjxcbiAgICBBIGV4dGVuZHMgTWluaW11bUFyZ3VtZW50VmFsdWVULFxuICAgIEkgZXh0ZW5kcyBNaW5pbXVtSW5wdXRWYWx1ZVQsXG4gICAgTyBleHRlbmRzIE1pbmltdW1PdXRwdXRWYWx1ZVQsXG4gID4oXG4gICAgbmFtZTogSm9iTmFtZSxcbiAgICBoYW5kbGVyOiBKb2JIYW5kbGVyPEEsIEksIE8+LFxuICAgIG9wdGlvbnM/OiBSZWdpc3RlckpvYk9wdGlvbnMsXG4gICk6IHZvaWQ7XG5cbiAgLyoqXG4gICAqIFJlZ2lzdGVyIGEgam9iIGhhbmRsZXIuIFRoZSBuYW1lIG11c3QgYmUgdW5pcXVlLlxuICAgKlxuICAgKiBAcGFyYW0gaGFuZGxlciBUaGUgZnVuY3Rpb24gdGhhdCB3aWxsIGJlIGNhbGxlZCBmb3IgdGhlIGpvYi5cbiAgICogQHBhcmFtIG9wdGlvbnMgQW4gb3B0aW9uYWwgbGlzdCBvZiBvcHRpb25zIHRvIG92ZXJyaWRlIHRoZSBoYW5kbGVyLiB7QHNlZSBSZWdpc3RlckpvYk9wdGlvbnN9XG4gICAqL1xuICByZWdpc3RlcjxBcmd1bWVudFQgZXh0ZW5kcyBKc29uVmFsdWUsIElucHV0VCBleHRlbmRzIEpzb25WYWx1ZSwgT3V0cHV0VCBleHRlbmRzIEpzb25WYWx1ZT4oXG4gICAgaGFuZGxlcjogSm9iSGFuZGxlcjxBcmd1bWVudFQsIElucHV0VCwgT3V0cHV0VD4sXG4gICAgLy8gVGhpcyB2ZXJzaW9uIE1VU1QgY29udGFpbiBhIG5hbWUuXG4gICAgb3B0aW9ucz86IFJlZ2lzdGVySm9iT3B0aW9ucyAmIHsgbmFtZTogc3RyaW5nIH0sXG4gICk6IHZvaWQ7XG5cbiAgcmVnaXN0ZXI8QXJndW1lbnRUIGV4dGVuZHMgSnNvblZhbHVlLCBJbnB1dFQgZXh0ZW5kcyBKc29uVmFsdWUsIE91dHB1dFQgZXh0ZW5kcyBKc29uVmFsdWU+KFxuICAgIG5hbWVPckhhbmRsZXI6IEpvYk5hbWUgfCBKb2JIYW5kbGVyPEFyZ3VtZW50VCwgSW5wdXRULCBPdXRwdXRUPixcbiAgICBoYW5kbGVyT3JPcHRpb25zOiBKb2JIYW5kbGVyPEFyZ3VtZW50VCwgSW5wdXRULCBPdXRwdXRUPiB8IFJlZ2lzdGVySm9iT3B0aW9ucyA9IHt9LFxuICAgIG9wdGlvbnM6IFJlZ2lzdGVySm9iT3B0aW9ucyA9IHt9LFxuICApOiB2b2lkIHtcbiAgICAvLyBTd2l0Y2ggb24gdGhlIGFyZ3VtZW50cy5cbiAgICBpZiAodHlwZW9mIG5hbWVPckhhbmRsZXIgPT0gJ3N0cmluZycpIHtcbiAgICAgIGlmICghaXNKb2JIYW5kbGVyKGhhbmRsZXJPck9wdGlvbnMpKSB7XG4gICAgICAgIC8vIFRoaXMgaXMgYW4gZXJyb3IuXG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0V4cGVjdGVkIGEgSm9iSGFuZGxlciBhcyBzZWNvbmQgYXJndW1lbnQuJyk7XG4gICAgICB9XG5cbiAgICAgIHRoaXMuX3JlZ2lzdGVyKG5hbWVPckhhbmRsZXIsIGhhbmRsZXJPck9wdGlvbnMsIG9wdGlvbnMpO1xuICAgIH0gZWxzZSBpZiAoaXNKb2JIYW5kbGVyKG5hbWVPckhhbmRsZXIpKSB7XG4gICAgICBpZiAodHlwZW9mIGhhbmRsZXJPck9wdGlvbnMgIT09ICdvYmplY3QnKSB7XG4gICAgICAgIC8vIFRoaXMgaXMgYW4gZXJyb3IuXG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0V4cGVjdGVkIGFuIG9iamVjdCBvcHRpb25zIGFzIHNlY29uZCBhcmd1bWVudC4nKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgbmFtZSA9IG9wdGlvbnMubmFtZSB8fCBuYW1lT3JIYW5kbGVyLmpvYkRlc2NyaXB0aW9uLm5hbWUgfHwgaGFuZGxlck9yT3B0aW9ucy5uYW1lO1xuICAgICAgaWYgKG5hbWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdFeHBlY3RlZCBuYW1lIHRvIGJlIGEgc3RyaW5nLicpO1xuICAgICAgfVxuXG4gICAgICB0aGlzLl9yZWdpc3RlcihuYW1lLCBuYW1lT3JIYW5kbGVyLCBvcHRpb25zKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignVW5yZWNvZ25pemVkIGFyZ3VtZW50cy4nKTtcbiAgICB9XG4gIH1cblxuICBwcm90ZWN0ZWQgX3JlZ2lzdGVyPFxuICAgIEFyZ3VtZW50VCBleHRlbmRzIEpzb25WYWx1ZSxcbiAgICBJbnB1dFQgZXh0ZW5kcyBKc29uVmFsdWUsXG4gICAgT3V0cHV0VCBleHRlbmRzIEpzb25WYWx1ZSxcbiAgPihcbiAgICBuYW1lOiBKb2JOYW1lLFxuICAgIGhhbmRsZXI6IEpvYkhhbmRsZXI8QXJndW1lbnRULCBJbnB1dFQsIE91dHB1dFQ+LFxuICAgIG9wdGlvbnM6IFJlZ2lzdGVySm9iT3B0aW9ucyxcbiAgKTogdm9pZCB7XG4gICAgaWYgKHRoaXMuX2pvYk5hbWVzLmhhcyhuYW1lKSkge1xuICAgICAgLy8gV2Ugc2hvdWxkbid0IGFsbG93IGNvbmZsaWN0cy5cbiAgICAgIHRocm93IG5ldyBKb2JOYW1lQWxyZWFkeVJlZ2lzdGVyZWRFeGNlcHRpb24obmFtZSk7XG4gICAgfVxuXG4gICAgLy8gTWVyZ2UgYWxsIGZpZWxkcyB3aXRoIHRoZSBvbmVzIGluIHRoZSBoYW5kbGVyICh0byBtYWtlIHN1cmUgd2UgcmVzcGVjdCB0aGUgaGFuZGxlcikuXG4gICAgY29uc3QgYXJndW1lbnQgPSBzY2hlbWEubWVyZ2VTY2hlbWFzKFxuICAgICAgaGFuZGxlci5qb2JEZXNjcmlwdGlvbi5hcmd1bWVudCxcbiAgICAgIG9wdGlvbnMuYXJndW1lbnQsXG4gICAgKTtcbiAgICBjb25zdCBpbnB1dCA9IHNjaGVtYS5tZXJnZVNjaGVtYXMoXG4gICAgICBoYW5kbGVyLmpvYkRlc2NyaXB0aW9uLmlucHV0LFxuICAgICAgb3B0aW9ucy5pbnB1dCxcbiAgICApO1xuICAgIGNvbnN0IG91dHB1dCA9IHNjaGVtYS5tZXJnZVNjaGVtYXMoXG4gICAgICBoYW5kbGVyLmpvYkRlc2NyaXB0aW9uLm91dHB1dCxcbiAgICAgIG9wdGlvbnMub3V0cHV0LFxuICAgICk7XG5cbiAgICAvLyBDcmVhdGUgdGhlIGpvYiBkZXNjcmlwdGlvbi5cbiAgICBjb25zdCBqb2JEZXNjcmlwdGlvbjogSm9iRGVzY3JpcHRpb24gPSB7XG4gICAgICBuYW1lLFxuICAgICAgYXJndW1lbnQsXG4gICAgICBvdXRwdXQsXG4gICAgICBpbnB1dCxcbiAgICB9O1xuXG4gICAgdGhpcy5fam9iTmFtZXMuc2V0KG5hbWUsIE9iamVjdC5hc3NpZ24oaGFuZGxlci5iaW5kKHVuZGVmaW5lZCksIHsgam9iRGVzY3JpcHRpb24gfSkpO1xuICB9XG5cbiAgLyoqXG4gICAqIFJldHVybnMgdGhlIGpvYiBuYW1lcyBvZiBhbGwgam9icy5cbiAgICovXG4gIGdldEpvYk5hbWVzKCk6IEpvYk5hbWVbXSB7XG4gICAgcmV0dXJuIFsuLi50aGlzLl9qb2JOYW1lcy5rZXlzKCldO1xuICB9XG59XG4iXX0=