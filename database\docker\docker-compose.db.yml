version: '3.8'

services:
  db:
    build:
      context: ../
      dockerfile: Dockerfile
    restart: always
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
      POSTGRES_DB: yourdb
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ../migrations:/docker-entrypoint-initdb.d/migrations
      - ../seeders:/docker-entrypoint-initdb.d/seeders
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d yourdb"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  db_data:
