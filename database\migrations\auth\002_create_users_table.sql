-- 创建用户表
-- 创建时间: 2024-01-01
-- 描述: 多租户系统的用户表，支持账号冻结/解冻逻辑

CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    email VARCHAR(255) NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '加密后的密码',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-冻结，3-禁用',
    login_fail_count INTEGER NOT NULL DEFAULT 0 COMMENT '连续登录失败次数',
    last_login_at TIMESTAMP NULL COMMENT '上次登录时间',
    last_login_ip VARCHAR(45) NULL COMMENT '上次登录IP（支持IPv6）',
    last_login_device VARCHAR(500) NULL COMMENT '上次登录设备指纹',
    role_id BIGINT NULL COMMENT '默认角色ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    CONSTRAINT fk_users_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- 创建唯一性约束
ALTER TABLE users ADD CONSTRAINT uk_users_tenant_username UNIQUE (tenant_id, username);
ALTER TABLE users ADD CONSTRAINT uk_users_tenant_email UNIQUE (tenant_id, email);

-- 创建索引
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_role_id ON users(role_id);
CREATE INDEX idx_users_last_login_at ON users(last_login_at);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 添加表注释
COMMENT ON TABLE users IS '用户表 - 多租户系统的用户基础信息，支持账号状态管理';

-- 添加字段注释
COMMENT ON COLUMN users.id IS '用户ID，主键';
COMMENT ON COLUMN users.tenant_id IS '租户ID，外键关联tenants表';
COMMENT ON COLUMN users.username IS '用户名，租户内唯一';
COMMENT ON COLUMN users.email IS '邮箱地址，租户内唯一';
COMMENT ON COLUMN users.password_hash IS '加密后的密码哈希值';
COMMENT ON COLUMN users.status IS '用户状态：1-正常，2-冻结，3-禁用';
COMMENT ON COLUMN users.login_fail_count IS '连续登录失败次数，用于账号锁定逻辑';
COMMENT ON COLUMN users.last_login_at IS '最后一次成功登录时间';
COMMENT ON COLUMN users.last_login_ip IS '最后一次登录IP地址';
COMMENT ON COLUMN users.last_login_device IS '最后一次登录设备指纹信息';
COMMENT ON COLUMN users.role_id IS '用户默认角色ID';
COMMENT ON COLUMN users.created_at IS '创建时间';
COMMENT ON COLUMN users.updated_at IS '更新时间';

-- 创建更新时间触发器
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
