import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from './filters/http-exception.filter';
import { LoggerService } from './logger/logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 使用 Winston Logger 替换默认 Logger
  const loggerService = app.get(LoggerService);
  app.useLogger(loggerService);

  // 全局前缀
  app.setGlobalPrefix('api');

  // 全局异常过滤器
  app.useGlobalFilters(new HttpExceptionFilter());

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // CORS 配置
  app.enableCors({
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true,
  });

  // Swagger 配置
  const config = new DocumentBuilder()
    .setTitle('WHT SaaS API')
    .setDescription('WHT SaaS Platform API Documentation')
    .setVersion('1.0')
    .addTag('Health', '健康检查相关接口')
    .addTag('Tasks', '任务管理相关接口')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  const port = process.env.PORT || 4000;
  await app.listen(port);

  loggerService.log(`🚀 Application is running on: http://localhost:${port}`, {
    port: Number(port),
    environment: process.env.NODE_ENV || 'development',
  });
  loggerService.log(`📚 Swagger documentation: http://localhost:${port}/api/docs`);
}
bootstrap();
