-- 默认权限种子数据
-- 环境: 开发环境
-- 描述: 创建系统基础权限

INSERT INTO permissions (name, code, description, created_at, updated_at) VALUES 
-- 用户管理权限
('查看用户', 'user:read', '查看用户列表和详情', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('创建用户', 'user:create', '创建新用户', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('编辑用户', 'user:update', '编辑用户信息', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('删除用户', 'user:delete', '删除用户', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 角色管理权限
('查看角色', 'role:read', '查看角色列表和详情', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('创建角色', 'role:create', '创建新角色', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('编辑角色', 'role:update', '编辑角色信息', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('删除角色', 'role:delete', '删除角色', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 权限管理权限
('查看权限', 'permission:read', '查看权限列表', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('分配权限', 'permission:assign', '为角色分配权限', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 租户管理权限
('查看租户', 'tenant:read', '查看租户信息', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('编辑租户', 'tenant:update', '编辑租户信息', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 系统管理权限
('系统设置', 'system:config', '系统配置管理', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('查看日志', 'system:logs', '查看系统日志', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 任务管理权限
('查看任务', 'task:read', '查看任务列表和详情', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('创建任务', 'task:create', '创建新任务', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('编辑任务', 'task:update', '编辑任务信息', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('删除任务', 'task:delete', '删除任务', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)

ON CONFLICT (code) DO NOTHING;
