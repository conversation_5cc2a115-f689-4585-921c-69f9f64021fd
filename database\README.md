# 数据库目录

## 目录结构
```
database/
├── Dockerfile              # 数据库镜像构建文件
├── docker/                 # Docker 配置
│   └── docker-compose.db.yml
├── migrations/             # 数据库迁移文件
│   ├── 001_initial_schema.sql
│   ├── 002_users_table.sql
│   └── 003_tasks_table.sql
├── seeders/               # 种子数据文件
│   ├── dev_users.sql
│   ├── dev_tasks.sql
│   └── test_data.sql
├── schema/                # 数据库结构定义
│   └── schema.sql
└── scripts/               # 数据库管理脚本
    ├── init-database.sh
    ├── init-sonar-db.sh
    ├── reset-db.js
    ├── reset-db.sh
    └── reset-db.ps1
```

## 一键重置数据库

### 使用方法
```bash
# 重置数据库（推荐）
pnpm run db:reset

# 或者直接运行脚本
node database/scripts/reset-db.js        # Node.js 版本
bash database/scripts/reset-db.sh        # Linux/Mac
powershell database/scripts/reset-db.ps1 # Windows
```

### 重置过程
1. 停止数据库服务
2. 删除数据库容器和卷
3. 重新构建数据库镜像
4. 启动新的数据库容器
5. 自动执行迁移和种子数据

## 数据库管理命令

```bash
# 启动数据库
pnpm run db:start

# 停止数据库
pnpm run db:stop

# 查看数据库日志
pnpm run db:logs

# 完全重置并启动开发环境
pnpm run dev:clean
```

## 文件说明

### migrations/
- 按数字顺序命名的 SQL 迁移文件
- 每个文件包含特定的数据库结构变更
- 在数据库初始化时按顺序执行

### seeders/
- 测试和开发用的初始数据
- `dev_*.sql` - 开发环境数据
- `test_*.sql` - 测试环境数据

### schema/
- `schema.sql` - 完整的数据库结构定义
- 用于快速了解整体数据库设计

### scripts/
- 数据库管理和维护脚本
- 支持跨平台（Windows/Linux/Mac）

## 开发流程

1. **添加新表**: 在 `migrations/` 中创建新的迁移文件
2. **添加测试数据**: 在 `seeders/` 中添加相应的种子数据
3. **更新 schema**: 更新 `schema/schema.sql` 反映最新结构
4. **重置测试**: 运行 `pnpm run db:reset` 验证更改
