export declare namespace colors {
    const reset: (x: string) => string;
    const bold: (x: string) => string;
    const dim: (x: string) => string;
    const italic: (x: string) => string;
    const underline: (x: string) => string;
    const inverse: (x: string) => string;
    const hidden: (x: string) => string;
    const strikethrough: (x: string) => string;
    const black: (x: string) => string;
    const red: (x: string) => string;
    const green: (x: string) => string;
    const yellow: (x: string) => string;
    const blue: (x: string) => string;
    const magenta: (x: string) => string;
    const cyan: (x: string) => string;
    const white: (x: string) => string;
    const grey: (x: string) => string;
    const gray: (x: string) => string;
    const bgBlack: (x: string) => string;
    const bgRed: (x: string) => string;
    const bgGreen: (x: string) => string;
    const bgYellow: (x: string) => string;
    const bgBlue: (x: string) => string;
    const bgMagenta: (x: string) => string;
    const bgCyan: (x: string) => string;
    const bgWhite: (x: string) => string;
}
