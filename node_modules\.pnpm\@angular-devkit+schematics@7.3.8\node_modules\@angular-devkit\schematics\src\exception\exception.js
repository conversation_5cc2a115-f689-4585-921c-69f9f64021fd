"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const core_1 = require("@angular-devkit/core");
// Used by schematics to throw exceptions.
class SchematicsException extends core_1.BaseException {
}
exports.SchematicsException = SchematicsException;
// Exceptions
class FileDoesNotExistException extends core_1.BaseException {
    constructor(path) { super(`Path "${path}" does not exist.`); }
}
exports.FileDoesNotExistException = FileDoesNotExistException;
class FileAlreadyExistException extends core_1.BaseException {
    constructor(path) { super(`Path "${path}" already exist.`); }
}
exports.FileAlreadyExistException = FileAlreadyExistException;
class ContentHasMutatedException extends core_1.BaseException {
    constructor(path) {
        super(`Content at path "${path}" has changed between the start and the end of an update.`);
    }
}
exports.ContentHasMutatedException = ContentHasMutatedException;
class InvalidUpdateRecordException extends core_1.BaseException {
    constructor() { super(`Invalid record instance.`); }
}
exports.InvalidUpdateRecordException = InvalidUpdateRecordException;
class MergeConflictException extends core_1.BaseException {
    constructor(path) {
        super(`A merge conflicted on path "${path}".`);
    }
}
exports.MergeConflictException = MergeConflictException;
class UnsuccessfulWorkflowExecution extends core_1.BaseException {
    constructor() {
        super('Workflow did not execute successfully.');
    }
}
exports.UnsuccessfulWorkflowExecution = UnsuccessfulWorkflowExecution;
class UnimplementedException extends core_1.BaseException {
    constructor() { super('This function is unimplemented.'); }
}
exports.UnimplementedException = UnimplementedException;
//# sourceMappingURL=data:application/json;base64,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