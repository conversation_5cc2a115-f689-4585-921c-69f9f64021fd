import { ApiProperty } from '@nestjs/swagger';

export class ApiResponseDto<T = any> {
  @ApiProperty({ description: '状态码', example: 200 })
  statusCode: number;

  @ApiProperty({ description: '响应消息', example: 'Success' })
  message: string;

  @ApiProperty({ description: '响应数据' })
  data?: T;

  @ApiProperty({ description: '时间戳', example: '2024-01-01T00:00:00.000Z' })
  timestamp: string;

  @ApiProperty({ description: '请求路径', example: '/api/tasks' })
  path: string;
}

export class PaginationDto {
  @ApiProperty({ description: '页码', example: 1, minimum: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  limit: number;

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}

export class ErrorResponseDto {
  @ApiProperty({ description: '错误状态码', example: 400 })
  statusCode: number;

  @ApiProperty({ description: '错误消息', example: 'Bad Request' })
  message: string;

  @ApiProperty({ description: '错误详情', example: 'Validation failed' })
  error?: string;

  @ApiProperty({ description: '时间戳', example: '2024-01-01T00:00:00.000Z' })
  timestamp: string;

  @ApiProperty({ description: '请求路径', example: '/api/tasks' })
  path: string;
}
