"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const update_buffer_1 = require("../utility/update-buffer");
const sink_1 = require("./sink");
class HostSink extends sink_1.SimpleSinkBase {
    constructor(_host, _force = false) {
        super();
        this._host = _host;
        this._force = _force;
        this._filesToDelete = new Set();
        this._filesToRename = new Set();
        this._filesToCreate = new Map();
        this._filesToUpdate = new Map();
    }
    _validateCreateAction(action) {
        return this._force ? rxjs_1.EMPTY : super._validateCreateAction(action);
    }
    _validateFileExists(p) {
        if (this._filesToCreate.has(p) || this._filesToUpdate.has(p)) {
            return rxjs_1.of(true);
        }
        else if (this._filesToDelete.has(p)) {
            return rxjs_1.of(false);
        }
        else if ([...this._filesToRename.values()].some(([from]) => from == p)) {
            return rxjs_1.of(false);
        }
        else {
            return this._host.exists(p);
        }
    }
    _overwriteFile(path, content) {
        this._filesToUpdate.set(path, new update_buffer_1.UpdateBuffer(content));
        return rxjs_1.EMPTY;
    }
    _createFile(path, content) {
        this._filesToCreate.set(path, new update_buffer_1.UpdateBuffer(content));
        return rxjs_1.EMPTY;
    }
    _renameFile(from, to) {
        this._filesToRename.add([from, to]);
        return rxjs_1.EMPTY;
    }
    _deleteFile(path) {
        if (this._filesToCreate.has(path)) {
            this._filesToCreate.delete(path);
            this._filesToUpdate.delete(path);
        }
        else {
            this._filesToDelete.add(path);
        }
        return rxjs_1.EMPTY;
    }
    _done() {
        // Really commit everything to the actual filesystem.
        return rxjs_1.concat(rxjs_1.from([...this._filesToDelete.values()]).pipe(operators_1.concatMap(path => this._host.delete(path))), rxjs_1.from([...this._filesToRename.entries()]).pipe(operators_1.concatMap(([_, [path, to]]) => this._host.rename(path, to))), rxjs_1.from([...this._filesToCreate.entries()]).pipe(operators_1.concatMap(([path, buffer]) => {
            return this._host.write(path, buffer.generate());
        })), rxjs_1.from([...this._filesToUpdate.entries()]).pipe(operators_1.concatMap(([path, buffer]) => {
            return this._host.write(path, buffer.generate());
        }))).pipe(operators_1.reduce(() => { }));
    }
}
exports.HostSink = HostSink;
//# sourceMappingURL=data:application/json;base64,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