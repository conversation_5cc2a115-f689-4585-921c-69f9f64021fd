version: '3.8'
services:
  backend:
    build:
      context: ./apps/backend
      dockerfile: backend-dev.dockerfile
    command: pnpm run start:dev
    volumes:
      - ./apps/backend:/app
      - /app/node_modules
    ports:
      - "4000:4000"
    environment:
      DATABASE_URL: ****************************/yourdb
      NODE_ENV: development
    depends_on:
      - db

  frontend:
    build:
      context: ./apps/frontend
      dockerfile: frontend-dev.dockerfile
    command: pnpm run dev
    volumes:
      - ./apps/frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    environment:
      API_URL: http://backend:4000
      NODE_ENV: development

  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
      POSTGRES_DB: yourdb
    ports:
      - "5432:5432"
    volumes:
      - pg_data:/var/lib/postgresql/data

volumes:
  pg_data:
