"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function isJsonObject(value) {
    return value != null && typeof value === 'object' && !Array.isArray(value);
}
exports.isJsonObject = isJsonObject;
function isJsonArray(value) {
    return Array.isArray(value);
}
exports.isJsonArray = isJsonArray;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW50ZXJmYWNlLmpzIiwic291cmNlUm9vdCI6Ii4vIiwic291cmNlcyI6WyJwYWNrYWdlcy9hbmd1bGFyX2RldmtpdC9jb3JlL3NyYy9qc29uL2ludGVyZmFjZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOztBQWdIQSxTQUFnQixZQUFZLENBQUMsS0FBZ0I7SUFDM0MsT0FBTyxLQUFLLElBQUksSUFBSSxJQUFJLE9BQU8sS0FBSyxLQUFLLFFBQVEsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUM7QUFDN0UsQ0FBQztBQUZELG9DQUVDO0FBRUQsU0FBZ0IsV0FBVyxDQUFDLEtBQWdCO0lBQzFDLE9BQU8sS0FBSyxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQztBQUM5QixDQUFDO0FBRkQsa0NBRUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIEluYy4gQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5leHBvcnQgaW50ZXJmYWNlIFBvc2l0aW9uIHtcbiAgcmVhZG9ubHkgb2Zmc2V0OiBudW1iZXI7XG5cbiAgcmVhZG9ubHkgbGluZTogbnVtYmVyO1xuICByZWFkb25seSBjaGFyYWN0ZXI6IG51bWJlcjtcbn1cblxuXG5leHBvcnQgdHlwZSBKc29uQXN0Tm9kZSA9IEpzb25Bc3ROdW1iZXJcbiAgICB8IEpzb25Bc3RTdHJpbmdcbiAgICB8IEpzb25Bc3RJZGVudGlmaWVyXG4gICAgfCBKc29uQXN0QXJyYXlcbiAgICB8IEpzb25Bc3RPYmplY3RcbiAgICB8IEpzb25Bc3RDb25zdGFudEZhbHNlXG4gICAgfCBKc29uQXN0Q29uc3RhbnROdWxsXG4gICAgfCBKc29uQXN0Q29uc3RhbnRUcnVlO1xuXG5cbmV4cG9ydCBpbnRlcmZhY2UgSnNvbkFzdE5vZGVCYXNlIHtcbiAgcmVhZG9ubHkgc3RhcnQ6IFBvc2l0aW9uO1xuICByZWFkb25seSBlbmQ6IFBvc2l0aW9uO1xuICByZWFkb25seSB0ZXh0OiBzdHJpbmc7XG5cbiAgcmVhZG9ubHkgY29tbWVudHM/OiAoSnNvbkFzdENvbW1lbnQgfCBKc29uQXN0TXVsdGlsaW5lQ29tbWVudClbXTtcbn1cblxuXG5leHBvcnQgaW50ZXJmYWNlIEpzb25Bc3ROdW1iZXIgZXh0ZW5kcyBKc29uQXN0Tm9kZUJhc2Uge1xuICByZWFkb25seSBraW5kOiAnbnVtYmVyJztcbiAgcmVhZG9ubHkgdmFsdWU6IG51bWJlcjtcbn1cblxuXG5leHBvcnQgaW50ZXJmYWNlIEpzb25Bc3RTdHJpbmcgZXh0ZW5kcyBKc29uQXN0Tm9kZUJhc2Uge1xuICByZWFkb25seSBraW5kOiAnc3RyaW5nJztcbiAgcmVhZG9ubHkgdmFsdWU6IHN0cmluZztcbn1cblxuXG5leHBvcnQgaW50ZXJmYWNlIEpzb25Bc3RJZGVudGlmaWVyIGV4dGVuZHMgSnNvbkFzdE5vZGVCYXNlIHtcbiAgcmVhZG9ubHkga2luZDogJ2lkZW50aWZpZXInO1xuICByZWFkb25seSB2YWx1ZTogc3RyaW5nO1xufVxuXG5cbmV4cG9ydCBpbnRlcmZhY2UgSnNvbkFycmF5IGV4dGVuZHMgQXJyYXk8SnNvblZhbHVlPiB7fVxuXG5cbmV4cG9ydCBpbnRlcmZhY2UgSnNvbkFzdEFycmF5IGV4dGVuZHMgSnNvbkFzdE5vZGVCYXNlIHtcbiAgcmVhZG9ubHkga2luZDogJ2FycmF5JztcbiAgcmVhZG9ubHkgZWxlbWVudHM6IEpzb25Bc3ROb2RlW107XG4gIHJlYWRvbmx5IHZhbHVlOiBKc29uQXJyYXk7XG59XG5cblxuZXhwb3J0IGludGVyZmFjZSBKc29uT2JqZWN0IHtcbiAgW3Byb3A6IHN0cmluZ106IEpzb25WYWx1ZTtcbn1cblxuXG5leHBvcnQgaW50ZXJmYWNlIEpzb25Bc3RLZXlWYWx1ZSBleHRlbmRzIEpzb25Bc3ROb2RlQmFzZSB7XG4gIHJlYWRvbmx5IGtpbmQ6ICdrZXl2YWx1ZSc7XG4gIHJlYWRvbmx5IGtleTogSnNvbkFzdFN0cmluZyB8IEpzb25Bc3RJZGVudGlmaWVyO1xuICByZWFkb25seSB2YWx1ZTogSnNvbkFzdE5vZGU7XG59XG5cblxuZXhwb3J0IGludGVyZmFjZSBKc29uQXN0T2JqZWN0IGV4dGVuZHMgSnNvbkFzdE5vZGVCYXNlIHtcbiAgcmVhZG9ubHkga2luZDogJ29iamVjdCc7XG4gIHJlYWRvbmx5IHByb3BlcnRpZXM6IEpzb25Bc3RLZXlWYWx1ZVtdO1xuICByZWFkb25seSB2YWx1ZTogSnNvbk9iamVjdDtcbn1cblxuXG5leHBvcnQgaW50ZXJmYWNlIEpzb25Bc3RDb25zdGFudEZhbHNlIGV4dGVuZHMgSnNvbkFzdE5vZGVCYXNlIHtcbiAgcmVhZG9ubHkga2luZDogJ2ZhbHNlJztcbiAgcmVhZG9ubHkgdmFsdWU6IGZhbHNlO1xufVxuXG5cbmV4cG9ydCBpbnRlcmZhY2UgSnNvbkFzdENvbnN0YW50TnVsbCBleHRlbmRzIEpzb25Bc3ROb2RlQmFzZSB7XG4gIHJlYWRvbmx5IGtpbmQ6ICdudWxsJztcbiAgcmVhZG9ubHkgdmFsdWU6IG51bGw7XG59XG5cblxuZXhwb3J0IGludGVyZmFjZSBKc29uQXN0Q29uc3RhbnRUcnVlIGV4dGVuZHMgSnNvbkFzdE5vZGVCYXNlIHtcbiAgcmVhZG9ubHkga2luZDogJ3RydWUnO1xuICByZWFkb25seSB2YWx1ZTogdHJ1ZTtcbn1cblxuXG4vLyBMb29zZSBtb2RlIEFTVC5cbmV4cG9ydCBpbnRlcmZhY2UgSnNvbkFzdE11bHRpbGluZUNvbW1lbnQgZXh0ZW5kcyBKc29uQXN0Tm9kZUJhc2Uge1xuICByZWFkb25seSBraW5kOiAnbXVsdGljb21tZW50JztcbiAgcmVhZG9ubHkgY29udGVudDogc3RyaW5nO1xufVxuZXhwb3J0IGludGVyZmFjZSBKc29uQXN0Q29tbWVudCBleHRlbmRzIEpzb25Bc3ROb2RlQmFzZSB7XG4gIHJlYWRvbmx5IGtpbmQ6ICdjb21tZW50JztcbiAgcmVhZG9ubHkgY29udGVudDogc3RyaW5nO1xufVxuXG5cbmV4cG9ydCB0eXBlIEpzb25WYWx1ZSA9IEpzb25Bc3ROb2RlWyd2YWx1ZSddO1xuXG5leHBvcnQgZnVuY3Rpb24gaXNKc29uT2JqZWN0KHZhbHVlOiBKc29uVmFsdWUpOiB2YWx1ZSBpcyBKc29uT2JqZWN0IHtcbiAgcmV0dXJuIHZhbHVlICE9IG51bGwgJiYgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJiAhQXJyYXkuaXNBcnJheSh2YWx1ZSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0pzb25BcnJheSh2YWx1ZTogSnNvblZhbHVlKTogdmFsdWUgaXMgSnNvbkFycmF5IHtcbiAgcmV0dXJuIEFycmF5LmlzQXJyYXkodmFsdWUpO1xufVxuIl19