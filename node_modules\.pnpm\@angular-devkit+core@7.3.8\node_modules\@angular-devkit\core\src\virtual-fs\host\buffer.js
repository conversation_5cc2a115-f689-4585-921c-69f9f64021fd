"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function stringToFileBuffer(str) {
    // If we're in Node...
    if (typeof Buffer !== 'undefined' && typeof Buffer.from === 'function') {
        const buf = Buffer.from(str);
        const ab = new ArrayBuffer(buf.length);
        const view = new Uint8Array(ab);
        for (let i = 0; i < buf.length; ++i) {
            view[i] = buf[i];
        }
        return ab;
    }
    else if (typeof TextEncoder !== 'undefined') {
        // Modern browsers implement TextEncode.
        return new TextEncoder('utf-8').encode(str).buffer;
    }
    else {
        // Slowest method but sure to be compatible with every platform.
        const buf = new ArrayBuffer(str.length * 2); // 2 bytes for each char
        const bufView = new Uint16Array(buf);
        for (let i = 0, strLen = str.length; i < strLen; i++) {
            bufView[i] = str.charCodeAt(i);
        }
        return buf;
    }
}
exports.stringToFileBuffer = stringToFileBuffer;
exports.fileBuffer = (strings, ...values) => {
    return stringToFileBuffer(String.raw(strings, ...values));
};
function fileBufferToString(fileBuffer) {
    if (fileBuffer.toString.length == 1) {
        return fileBuffer.toString('utf-8');
    }
    else if (typeof Buffer !== 'undefined') {
        return Buffer.from(fileBuffer).toString('utf-8');
    }
    else if (typeof TextDecoder !== 'undefined') {
        // Modern browsers implement TextEncode.
        return new TextDecoder('utf-8').decode(new Uint8Array(fileBuffer));
    }
    else {
        // Slowest method but sure to be compatible with every platform.
        const bufView = new Uint8Array(fileBuffer);
        const bufLength = bufView.length;
        let result = '';
        let chunkLength = Math.pow(2, 16) - 1;
        // We have to chunk it because String.fromCharCode.apply will throw
        // `Maximum call stack size exceeded` on big inputs.
        for (let i = 0; i < bufLength; i += chunkLength) {
            if (i + chunkLength > bufLength) {
                chunkLength = bufLength - i;
            }
            result += String.fromCharCode.apply(null, bufView.subarray(i, i + chunkLength));
        }
        return result;
    }
}
exports.fileBufferToString = fileBufferToString;
//# sourceMappingURL=data:application/json;base64,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