"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const rxjs_1 = require("rxjs");
const exception_1 = require("../../exception");
const path_1 = require("../path");
class SimpleMemoryHost {
    constructor() {
        this._cache = new Map();
        this._watchers = new Map();
        this._cache.set(path_1.normalize('/'), this._newDirStats());
    }
    _newDirStats() {
        return {
            inspect() { return '<Directory>'; },
            isFile() { return false; },
            isDirectory() { return true; },
            size: 0,
            atime: new Date(),
            ctime: new Date(),
            mtime: new Date(),
            birthtime: new Date(),
            content: null,
        };
    }
    _newFileStats(content, oldStats) {
        return {
            inspect() { return `<File size(${content.byteLength})>`; },
            isFile() { return true; },
            isDirectory() { return false; },
            size: content.byteLength,
            atime: oldStats ? oldStats.atime : new Date(),
            ctime: new Date(),
            mtime: new Date(),
            birthtime: oldStats ? oldStats.birthtime : new Date(),
            content,
        };
    }
    _toAbsolute(path) {
        return path_1.isAbsolute(path) ? path : path_1.normalize('/' + path);
    }
    _updateWatchers(path, type) {
        const time = new Date();
        let currentPath = path;
        let parent = null;
        if (this._watchers.size == 0) {
            // Nothing to do if there's no watchers.
            return;
        }
        const maybeWatcher = this._watchers.get(currentPath);
        if (maybeWatcher) {
            maybeWatcher.forEach(watcher => {
                const [options, subject] = watcher;
                subject.next({ path, time, type });
                if (!options.persistent && type == 2 /* Deleted */) {
                    subject.complete();
                    this._watchers.delete(currentPath);
                }
            });
        }
        do {
            currentPath = parent !== null ? parent : currentPath;
            parent = path_1.dirname(currentPath);
            const maybeWatcher = this._watchers.get(currentPath);
            if (maybeWatcher) {
                maybeWatcher.forEach(watcher => {
                    const [options, subject] = watcher;
                    if (!options.recursive) {
                        return;
                    }
                    subject.next({ path, time, type });
                    if (!options.persistent && type == 2 /* Deleted */) {
                        subject.complete();
                        this._watchers.delete(currentPath);
                    }
                });
            }
        } while (parent != currentPath);
    }
    get capabilities() {
        return { synchronous: true };
    }
    /**
     * List of protected methods that give direct access outside the observables to the cache
     * and internal states.
     */
    _write(path, content) {
        path = this._toAbsolute(path);
        const old = this._cache.get(path);
        if (old && old.isDirectory()) {
            throw new exception_1.PathIsDirectoryException(path);
        }
        // Update all directories. If we find a file we know it's an invalid write.
        const fragments = path_1.split(path);
        let curr = path_1.normalize('/');
        for (const fr of fragments) {
            curr = path_1.join(curr, fr);
            const maybeStats = this._cache.get(fr);
            if (maybeStats) {
                if (maybeStats.isFile()) {
                    throw new exception_1.PathIsFileException(curr);
                }
            }
            else {
                this._cache.set(curr, this._newDirStats());
            }
        }
        // Create the stats.
        const stats = this._newFileStats(content, old);
        this._cache.set(path, stats);
        this._updateWatchers(path, old ? 0 /* Changed */ : 1 /* Created */);
    }
    _read(path) {
        path = this._toAbsolute(path);
        const maybeStats = this._cache.get(path);
        if (!maybeStats) {
            throw new exception_1.FileDoesNotExistException(path);
        }
        else if (maybeStats.isDirectory()) {
            throw new exception_1.PathIsDirectoryException(path);
        }
        else if (!maybeStats.content) {
            throw new exception_1.PathIsDirectoryException(path);
        }
        else {
            return maybeStats.content;
        }
    }
    _delete(path) {
        path = this._toAbsolute(path);
        if (this._isDirectory(path)) {
            for (const [cachePath] of this._cache.entries()) {
                if (cachePath.startsWith(path + path_1.NormalizedSep) || cachePath === path) {
                    this._cache.delete(cachePath);
                }
            }
        }
        else {
            this._cache.delete(path);
        }
        this._updateWatchers(path, 2 /* Deleted */);
    }
    _rename(from, to) {
        from = this._toAbsolute(from);
        to = this._toAbsolute(to);
        if (!this._cache.has(from)) {
            throw new exception_1.FileDoesNotExistException(from);
        }
        else if (this._cache.has(to)) {
            throw new exception_1.FileAlreadyExistException(to);
        }
        if (this._isDirectory(from)) {
            for (const path of this._cache.keys()) {
                if (path.startsWith(from + path_1.NormalizedSep)) {
                    const content = this._cache.get(path);
                    if (content) {
                        // We don't need to clone or extract the content, since we're moving files.
                        this._cache.set(path_1.join(to, path_1.NormalizedSep, path.slice(from.length)), content);
                    }
                }
            }
        }
        else {
            const content = this._cache.get(from);
            if (content) {
                const fragments = path_1.split(to);
                const newDirectories = [];
                let curr = path_1.normalize('/');
                for (const fr of fragments) {
                    curr = path_1.join(curr, fr);
                    const maybeStats = this._cache.get(fr);
                    if (maybeStats) {
                        if (maybeStats.isFile()) {
                            throw new exception_1.PathIsFileException(curr);
                        }
                    }
                    else {
                        newDirectories.push(curr);
                    }
                }
                for (const newDirectory of newDirectories) {
                    this._cache.set(newDirectory, this._newDirStats());
                }
                this._cache.delete(from);
                this._cache.set(to, content);
            }
        }
        this._updateWatchers(from, 3 /* Renamed */);
    }
    _list(path) {
        path = this._toAbsolute(path);
        if (this._isFile(path)) {
            throw new exception_1.PathIsFileException(path);
        }
        const fragments = path_1.split(path);
        const result = new Set();
        if (path !== path_1.NormalizedRoot) {
            for (const p of this._cache.keys()) {
                if (p.startsWith(path + path_1.NormalizedSep)) {
                    result.add(path_1.split(p)[fragments.length]);
                }
            }
        }
        else {
            for (const p of this._cache.keys()) {
                if (p.startsWith(path_1.NormalizedSep) && p !== path_1.NormalizedRoot) {
                    result.add(path_1.split(p)[1]);
                }
            }
        }
        return [...result];
    }
    _exists(path) {
        return !!this._cache.get(this._toAbsolute(path));
    }
    _isDirectory(path) {
        const maybeStats = this._cache.get(this._toAbsolute(path));
        return maybeStats ? maybeStats.isDirectory() : false;
    }
    _isFile(path) {
        const maybeStats = this._cache.get(this._toAbsolute(path));
        return maybeStats ? maybeStats.isFile() : false;
    }
    _stat(path) {
        const maybeStats = this._cache.get(this._toAbsolute(path));
        if (!maybeStats) {
            return null;
        }
        else {
            return maybeStats;
        }
    }
    _watch(path, options) {
        path = this._toAbsolute(path);
        const subject = new rxjs_1.Subject();
        let maybeWatcherArray = this._watchers.get(path);
        if (!maybeWatcherArray) {
            maybeWatcherArray = [];
            this._watchers.set(path, maybeWatcherArray);
        }
        maybeWatcherArray.push([options || {}, subject]);
        return subject.asObservable();
    }
    write(path, content) {
        return new rxjs_1.Observable(obs => {
            this._write(path, content);
            obs.next();
            obs.complete();
        });
    }
    read(path) {
        return new rxjs_1.Observable(obs => {
            const content = this._read(path);
            obs.next(content);
            obs.complete();
        });
    }
    delete(path) {
        return new rxjs_1.Observable(obs => {
            this._delete(path);
            obs.next();
            obs.complete();
        });
    }
    rename(from, to) {
        return new rxjs_1.Observable(obs => {
            this._rename(from, to);
            obs.next();
            obs.complete();
        });
    }
    list(path) {
        return new rxjs_1.Observable(obs => {
            obs.next(this._list(path));
            obs.complete();
        });
    }
    exists(path) {
        return new rxjs_1.Observable(obs => {
            obs.next(this._exists(path));
            obs.complete();
        });
    }
    isDirectory(path) {
        return new rxjs_1.Observable(obs => {
            obs.next(this._isDirectory(path));
            obs.complete();
        });
    }
    isFile(path) {
        return new rxjs_1.Observable(obs => {
            obs.next(this._isFile(path));
            obs.complete();
        });
    }
    // Some hosts may not support stat.
    stat(path) {
        return new rxjs_1.Observable(obs => {
            obs.next(this._stat(path));
            obs.complete();
        });
    }
    watch(path, options) {
        return this._watch(path, options);
    }
}
exports.SimpleMemoryHost = SimpleMemoryHost;
//# sourceMappingURL=data:application/json;base64,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