"use strict";
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
function mapObject(obj, mapper) {
    return Object.keys(obj).reduce((acc, k) => {
        acc[k] = mapper(k, obj[k]);
        return acc;
    }, {});
}
exports.mapObject = mapObject;
const copySymbol = Symbol();
// tslint:disable-next-line:no-any
function deepCopy(value) {
    if (Array.isArray(value)) {
        return value.map((o) => deepCopy(o));
    }
    else if (value && typeof value === 'object') {
        if (value[copySymbol]) {
            // This is a circular dependency. Just return the cloned value.
            return value[copySymbol];
        }
        if (value['toJSON']) {
            return JSON.parse(value['toJSON']());
        }
        const copy = new (Object.getPrototypeOf(value).constructor)();
        value[copySymbol] = copy;
        for (const key of Object.getOwnPropertyNames(value)) {
            copy[key] = deepCopy(value[key]);
        }
        value[copySymbol] = undefined;
        return copy;
    }
    else {
        return value;
    }
}
exports.deepCopy = deepCopy;
//# sourceMappingURL=data:application/json;base64,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