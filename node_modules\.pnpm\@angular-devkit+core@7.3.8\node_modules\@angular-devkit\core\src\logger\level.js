"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = require("./logger");
class LevelTransformLogger extends logger_1.Logger {
    constructor(name, parent = null, levelTransform) {
        super(name, parent);
        this.name = name;
        this.parent = parent;
        this.levelTransform = levelTransform;
    }
    log(level, message, metadata = {}) {
        return super.log(this.levelTransform(level), message, metadata);
    }
    createChild(name) {
        return new LevelTransformLogger(name, this, this.levelTransform);
    }
}
exports.LevelTransformLogger = LevelTransformLogger;
class LevelCapLogger extends LevelTransformLogger {
    constructor(name, parent = null, levelCap) {
        super(name, parent, (level) => {
            return (LevelCapLogger.levelMap[levelCap][level] || level);
        });
        this.name = name;
        this.parent = parent;
        this.levelCap = levelCap;
    }
}
LevelCapLogger.levelMap = {
    debug: { debug: 'debug', info: 'debug', warn: 'debug', error: 'debug', fatal: 'debug' },
    info: { debug: 'debug', info: 'info', warn: 'info', error: 'info', fatal: 'info' },
    warn: { debug: 'debug', info: 'info', warn: 'warn', error: 'warn', fatal: 'warn' },
    error: { debug: 'debug', info: 'info', warn: 'warn', error: 'error', fatal: 'error' },
    fatal: { debug: 'debug', info: 'info', warn: 'warn', error: 'error', fatal: 'fatal' },
};
exports.LevelCapLogger = LevelCapLogger;
//# sourceMappingURL=data:application/json;base64,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