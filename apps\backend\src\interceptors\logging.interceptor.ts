import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { LoggerService } from '../logger/logger.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private readonly logger: LoggerService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    
    // 生成追踪ID
    const traceId = uuidv4();
    const correlationId = request.headers['x-correlation-id'] as string || uuidv4();
    
    // 将追踪ID添加到请求对象中，供其他地方使用
    (request as any).traceId = traceId;
    (request as any).correlationId = correlationId;
    
    // 添加到响应头
    response.setHeader('X-Trace-Id', traceId);
    response.setHeader('X-Correlation-Id', correlationId);

    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const startTime = Date.now();

    // 记录请求开始
    this.logger.log('Request started', {
      traceId,
      correlationId,
      method,
      url,
      ip,
      userAgent,
    });

    return next.handle().pipe(
      tap((data) => {
        // 请求成功完成
        const responseTime = Date.now() - startTime;
        const statusCode = response.statusCode;

        this.logger.logApiRequest(method, url, statusCode, responseTime, {
          traceId,
          correlationId,
          ip,
          userAgent,
          responseSize: JSON.stringify(data).length,
        });
      }),
      catchError((error) => {
        // 请求出错
        const responseTime = Date.now() - startTime;
        const statusCode = error.status || 500;

        this.logger.logApiError(method, url, statusCode, error, {
          traceId,
          correlationId,
          ip,
          userAgent,
          responseTime,
        });

        throw error;
      }),
    );
  }
}
