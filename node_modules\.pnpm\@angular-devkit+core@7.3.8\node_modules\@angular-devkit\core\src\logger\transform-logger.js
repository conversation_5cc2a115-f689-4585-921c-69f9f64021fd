"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = require("./logger");
class TransformLogger extends logger_1.Logger {
    constructor(name, transform, parent = null) {
        super(name, parent);
        this._observable = transform(this._observable);
    }
}
exports.TransformLogger = TransformLogger;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidHJhbnNmb3JtLWxvZ2dlci5qcyIsInNvdXJjZVJvb3QiOiIuLyIsInNvdXJjZXMiOlsicGFja2FnZXMvYW5ndWxhcl9kZXZraXQvY29yZS9zcmMvbG9nZ2VyL3RyYW5zZm9ybS1sb2dnZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7QUFRQSxxQ0FBNEM7QUFHNUMsTUFBYSxlQUFnQixTQUFRLGVBQU07SUFDekMsWUFBWSxJQUFZLEVBQ1osU0FBaUUsRUFDakUsU0FBd0IsSUFBSTtRQUN0QyxLQUFLLENBQUMsSUFBSSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBQ3BCLElBQUksQ0FBQyxXQUFXLEdBQUcsU0FBUyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQztJQUNqRCxDQUFDO0NBQ0Y7QUFQRCwwQ0FPQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgSW5jLiBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cbmltcG9ydCB7IE9ic2VydmFibGUgfSBmcm9tICdyeGpzJztcbmltcG9ydCB7IExvZ0VudHJ5LCBMb2dnZXIgfSBmcm9tICcuL2xvZ2dlcic7XG5cblxuZXhwb3J0IGNsYXNzIFRyYW5zZm9ybUxvZ2dlciBleHRlbmRzIExvZ2dlciB7XG4gIGNvbnN0cnVjdG9yKG5hbWU6IHN0cmluZyxcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiAoc3RyZWFtOiBPYnNlcnZhYmxlPExvZ0VudHJ5PikgPT4gT2JzZXJ2YWJsZTxMb2dFbnRyeT4sXG4gICAgICAgICAgICAgIHBhcmVudDogTG9nZ2VyIHwgbnVsbCA9IG51bGwpIHtcbiAgICBzdXBlcihuYW1lLCBwYXJlbnQpO1xuICAgIHRoaXMuX29ic2VydmFibGUgPSB0cmFuc2Zvcm0odGhpcy5fb2JzZXJ2YWJsZSk7XG4gIH1cbn1cbiJdfQ==