# SonarQube 项目配置
sonar.projectKey=wht-saas
sonar.projectName=WHT SaaS Platform
sonar.projectVersion=1.0.0

# 源代码目录
sonar.sources=apps/frontend/src,apps/backend/src

# 测试目录
sonar.tests=apps/frontend/src,apps/backend/src,apps/backend/test

# 测试文件模式
sonar.test.inclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx,**/*.e2e-spec.ts

# 排除目录
sonar.exclusions=**/node_modules/**,**/.next/**,**/dist/**,**/coverage/**,**/build/**,**/*.d.ts,**/public/**

# 语言配置
sonar.typescript.node=node

# 前端项目配置
sonar.modules=frontend,backend

# 前端模块
frontend.sonar.projectName=Frontend (Next.js)
frontend.sonar.sources=apps/frontend/src
frontend.sonar.tests=apps/frontend/src
frontend.sonar.test.inclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx
frontend.sonar.exclusions=**/node_modules/**,**/.next/**,**/dist/**,**/coverage/**,**/public/**,**/*.d.ts
frontend.sonar.typescript.lcov.reportPaths=apps/frontend/coverage/lcov.info

# 后端模块
backend.sonar.projectName=Backend (NestJS)
backend.sonar.sources=apps/backend/src
backend.sonar.tests=apps/backend/src,apps/backend/test
backend.sonar.test.inclusions=**/*.spec.ts,**/*.e2e-spec.ts
backend.sonar.exclusions=**/node_modules/**,**/dist/**,**/coverage/**
backend.sonar.typescript.lcov.reportPaths=apps/backend/coverage/lcov.info

# 编码
sonar.sourceEncoding=UTF-8

# 服务器配置
sonar.host.url=http://localhost:9000
