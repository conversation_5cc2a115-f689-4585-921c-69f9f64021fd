# PostgreSQL 数据库镜像
FROM postgres:15

# 设置环境变量
ENV POSTGRES_DB=yourdb
ENV POSTGRES_USER=user
ENV POSTGRES_PASSWORD=pass

# 创建必要目录
RUN mkdir -p /docker-entrypoint-initdb.d/migrations
RUN mkdir -p /docker-entrypoint-initdb.d/seeders

# 复制初始化脚本
COPY scripts/init-sonar-db.sh /docker-entrypoint-initdb.d/01-init-sonar-db.sh
COPY migrations/*.sql /docker-entrypoint-initdb.d/migrations/
COPY seeders/*.sql /docker-entrypoint-initdb.d/seeders/

# 复制主初始化脚本
COPY scripts/init-database.sh /docker-entrypoint-initdb.d/02-init-database.sh

# 设置执行权限
RUN chmod +x /docker-entrypoint-initdb.d/*.sh

# 暴露端口
EXPOSE 5432

# 设置数据目录
VOLUME ["/var/lib/postgresql/data"]
