-- 默认用户种子数据
-- 环境: 开发环境
-- 描述: 创建默认测试用户

-- 注意：这里的密码哈希是 'password123' 的 bcrypt 哈希值
-- 在实际使用时应该使用更安全的密码
INSERT INTO users (
    id, tenant_id, username, email, password_hash, status, 
    login_fail_count, role_id, created_at, updated_at
) VALUES 
(1, 1, 'admin', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 0, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 1, 'manager', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 0, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3, 1, 'user', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 0, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (tenant_id, username) DO NOTHING;

-- 重置序列
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
