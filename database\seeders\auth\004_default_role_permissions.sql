-- 默认角色权限关联种子数据
-- 环境: 开发环境
-- 描述: 为默认角色分配权限

-- 超级管理员拥有所有权限
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 1, p.id, CURRENT_TIMESTAMP
FROM permissions p
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 管理员权限（除了系统设置）
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 2, p.id, CURRENT_TIMESTAMP
FROM permissions p
WHERE p.code IN (
    'user:read', 'user:create', 'user:update', 'user:delete',
    'role:read', 'role:create', 'role:update', 'role:delete',
    'permission:read', 'permission:assign',
    'tenant:read', 'tenant:update',
    'system:logs'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 普通用户权限（只有基础查看和任务管理）
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 3, p.id, CURRENT_TIMESTAMP
FROM permissions p
WHERE p.code IN (
    'user:read',
    'role:read',
    'permission:read',
    'task:read', 'task:create', 'task:update'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;
