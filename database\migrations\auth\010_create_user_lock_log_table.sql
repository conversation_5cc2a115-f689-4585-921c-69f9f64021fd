-- 创建账号冻结日志表
-- 创建时间: 2024-01-01
-- 描述: 用户账号冻结和解冻的审计日志

CREATE TABLE IF NOT EXISTS user_lock_log (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    ip_address VARCHAR(45) NULL COMMENT '触发冻结的IP地址',
    lock_type VARCHAR(50) NOT NULL COMMENT '冻结类型：account-账号冻结，ip-IP冻结',
    reason VARCHAR(500) NOT NULL COMMENT '冻结原因',
    locked_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '冻结时间',
    unlock_at TIMESTAMP NULL COMMENT '解冻时间',
    operator_id BIGINT NULL COMMENT '操作人ID（0或NULL表示系统自动）',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-有效冻结，2-已解冻',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    CONSTRAINT fk_user_lock_log_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_lock_log_operator_id FOREIGN KEY (operator_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- 检查约束
    CONSTRAINT chk_user_lock_log_lock_type CHECK (lock_type IN ('account', 'ip')),
    CONSTRAINT chk_user_lock_log_status CHECK (status IN (1, 2))
);

-- 创建索引
CREATE INDEX idx_user_lock_log_user_id ON user_lock_log(user_id);
CREATE INDEX idx_user_lock_log_ip_address ON user_lock_log(ip_address);
CREATE INDEX idx_user_lock_log_lock_type ON user_lock_log(lock_type);
CREATE INDEX idx_user_lock_log_status ON user_lock_log(status);
CREATE INDEX idx_user_lock_log_locked_at ON user_lock_log(locked_at);
CREATE INDEX idx_user_lock_log_operator_id ON user_lock_log(operator_id);
CREATE INDEX idx_user_lock_log_created_at ON user_lock_log(created_at);

-- 创建复合索引（查询用户的有效冻结记录）
CREATE INDEX idx_user_lock_log_user_status ON user_lock_log(user_id, status);
CREATE INDEX idx_user_lock_log_user_type_status ON user_lock_log(user_id, lock_type, status);

-- 添加表注释
COMMENT ON TABLE user_lock_log IS '用户冻结日志表 - 记录用户账号冻结和解冻的审计信息';

-- 添加字段注释
COMMENT ON COLUMN user_lock_log.id IS '冻结日志ID，主键';
COMMENT ON COLUMN user_lock_log.user_id IS '用户ID，外键关联users表';
COMMENT ON COLUMN user_lock_log.ip_address IS '触发冻结的IP地址';
COMMENT ON COLUMN user_lock_log.lock_type IS '冻结类型：account-账号冻结，ip-IP冻结';
COMMENT ON COLUMN user_lock_log.reason IS '冻结原因详细描述';
COMMENT ON COLUMN user_lock_log.locked_at IS '账号冻结时间';
COMMENT ON COLUMN user_lock_log.unlock_at IS '账号解冻时间，NULL表示未解冻';
COMMENT ON COLUMN user_lock_log.operator_id IS '操作人ID，NULL或0表示系统自动操作';
COMMENT ON COLUMN user_lock_log.status IS '冻结状态：1-有效冻结，2-已解冻';
COMMENT ON COLUMN user_lock_log.created_at IS '记录创建时间';
COMMENT ON COLUMN user_lock_log.updated_at IS '记录更新时间';

-- 创建更新时间触发器
CREATE TRIGGER update_user_lock_log_updated_at 
    BEFORE UPDATE ON user_lock_log 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
