"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;base64,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