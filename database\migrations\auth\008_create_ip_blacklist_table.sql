-- 创建IP黑名单表
-- 创建时间: 2024-01-01
-- 描述: IP黑名单管理，用于风控和安全防护

CREATE TABLE IF NOT EXISTS ip_blacklist (
    id BIGSERIAL PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址（支持IPv4和IPv6）',
    reason VARCHAR(500) NOT NULL COMMENT '封禁原因',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-有效，2-已解禁',
    banned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '封禁时间',
    unbanned_at TIMESTAMP NULL COMMENT '解禁时间',
    operator_id BIGINT NULL COMMENT '操作人ID（管理员）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    CONSTRAINT fk_ip_blacklist_operator_id FOREIGN KEY (operator_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX idx_ip_blacklist_ip_address ON ip_blacklist(ip_address);
CREATE INDEX idx_ip_blacklist_status ON ip_blacklist(status);
CREATE INDEX idx_ip_blacklist_banned_at ON ip_blacklist(banned_at);
CREATE INDEX idx_ip_blacklist_operator_id ON ip_blacklist(operator_id);
CREATE INDEX idx_ip_blacklist_created_at ON ip_blacklist(created_at);

-- 创建复合索引（查询有效的IP黑名单）
CREATE INDEX idx_ip_blacklist_ip_status ON ip_blacklist(ip_address, status);

-- 添加表注释
COMMENT ON TABLE ip_blacklist IS 'IP黑名单表 - 用于风控和安全防护的IP封禁管理';

-- 添加字段注释
COMMENT ON COLUMN ip_blacklist.id IS 'IP黑名单ID，主键';
COMMENT ON COLUMN ip_blacklist.ip_address IS 'IP地址，支持IPv4和IPv6格式';
COMMENT ON COLUMN ip_blacklist.reason IS '封禁原因描述';
COMMENT ON COLUMN ip_blacklist.status IS 'IP状态：1-有效封禁，2-已解禁';
COMMENT ON COLUMN ip_blacklist.banned_at IS 'IP封禁时间';
COMMENT ON COLUMN ip_blacklist.unbanned_at IS 'IP解禁时间，NULL表示未解禁';
COMMENT ON COLUMN ip_blacklist.operator_id IS '操作人ID，关联users表，NULL表示系统自动操作';
COMMENT ON COLUMN ip_blacklist.created_at IS '记录创建时间';
COMMENT ON COLUMN ip_blacklist.updated_at IS '记录更新时间';

-- 创建更新时间触发器
CREATE TRIGGER update_ip_blacklist_updated_at 
    BEFORE UPDATE ON ip_blacklist 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
