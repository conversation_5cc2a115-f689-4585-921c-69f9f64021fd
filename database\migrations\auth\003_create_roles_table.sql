-- 创建角色表
-- 创建时间: 2024-01-01
-- 描述: 多租户系统的角色表，支持租户级别的角色管理

CREATE TABLE IF NOT EXISTS roles (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    name VARCHAR(100) NOT NULL COMMENT '角色名称',
    code VARCHAR(100) NOT NULL COMMENT '角色编码',
    description VARCHAR(500) NULL COMMENT '角色说明',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    CONSTRAINT fk_roles_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- 创建唯一性约束
ALTER TABLE roles ADD CONSTRAINT uk_roles_tenant_code UNIQUE (tenant_id, code);
ALTER TABLE roles ADD CONSTRAINT uk_roles_tenant_name UNIQUE (tenant_id, name);

-- 创建索引
CREATE INDEX idx_roles_tenant_id ON roles(tenant_id);
CREATE INDEX idx_roles_code ON roles(code);
CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_created_at ON roles(created_at);

-- 添加表注释
COMMENT ON TABLE roles IS '角色表 - 多租户系统的角色定义，租户级别隔离';

-- 添加字段注释
COMMENT ON COLUMN roles.id IS '角色ID，主键';
COMMENT ON COLUMN roles.tenant_id IS '租户ID，外键关联tenants表';
COMMENT ON COLUMN roles.name IS '角色名称，租户内唯一';
COMMENT ON COLUMN roles.code IS '角色编码，租户内唯一，用于程序识别';
COMMENT ON COLUMN roles.description IS '角色描述信息';
COMMENT ON COLUMN roles.created_at IS '创建时间';
COMMENT ON COLUMN roles.updated_at IS '更新时间';

-- 创建更新时间触发器
CREATE TRIGGER update_roles_updated_at 
    BEFORE UPDATE ON roles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
