"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Messages that can be sent TO a job. The job needs to listen to those.
 */
var JobInboundMessageKind;
(function (JobInboundMessageKind) {
    JobInboundMessageKind["Ping"] = "ip";
    JobInboundMessageKind["Stop"] = "is";
    // Channel specific messages.
    JobInboundMessageKind["Input"] = "in";
    // Input channel does not allow completion / error. Erroring this will just close the Subject
    // but not notify the job.
})(JobInboundMessageKind = exports.JobInboundMessageKind || (exports.JobInboundMessageKind = {}));
/**
 * Kind of messages that can be outputted from a job.
 */
var JobOutboundMessageKind;
(function (JobOutboundMessageKind) {
    // Lifecycle specific messages.
    JobOutboundMessageKind["OnReady"] = "c";
    JobOutboundMessageKind["Start"] = "s";
    JobOutboundMessageKind["End"] = "e";
    JobOutboundMessageKind["Pong"] = "p";
    // Feedback messages.
    JobOutboundMessageKind["Log"] = "l";
    JobOutboundMessageKind["Output"] = "o";
    // Channel specific messages.
    JobOutboundMessageKind["ChannelCreate"] = "cn";
    JobOutboundMessageKind["ChannelMessage"] = "cm";
    JobOutboundMessageKind["ChannelError"] = "ce";
    JobOutboundMessageKind["ChannelComplete"] = "cc";
})(JobOutboundMessageKind = exports.JobOutboundMessageKind || (exports.JobOutboundMessageKind = {}));
/**
 * The state of a job. These are changed as the job reports a new state through its messages.
 */
var JobState;
(function (JobState) {
    /**
     * The job was queued and is waiting to start.
     */
    JobState["Queued"] = "queued";
    /**
     * The job description was found, its dependencies (see "Synchronizing and Dependencies")
     * are done running, and the job's argument is validated and the job's code will be executed.
     */
    JobState["Ready"] = "ready";
    /**
     * The job has been started. The job implementation is expected to send this as soon as its
     * work is starting.
     */
    JobState["Started"] = "started";
    /**
     * The job has ended and is done running.
     */
    JobState["Ended"] = "ended";
    /**
     * An error occured and the job stopped because of internal state.
     */
    JobState["Errored"] = "errored";
})(JobState = exports.JobState || (exports.JobState = {}));
function isJobHandler(value) {
    return typeof value == 'function'
        && typeof value.jobDescription == 'object'
        && value.jobDescription !== null;
}
exports.isJobHandler = isJobHandler;
//# sourceMappingURL=data:application/json;base64,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