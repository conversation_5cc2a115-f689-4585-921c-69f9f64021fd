"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const interface_1 = require("../interface");
const allTypes = ['string', 'integer', 'number', 'object', 'array', 'boolean', 'null'];
function getTypesOfSchema(schema) {
    if (!schema) {
        return new Set();
    }
    if (schema === true) {
        return new Set(allTypes);
    }
    let potentials;
    if (typeof schema.type === 'string') {
        potentials = new Set([schema.type]);
    }
    else if (Array.isArray(schema.type)) {
        potentials = new Set(schema.type);
    }
    else if (interface_1.isJsonArray(schema.enum)) {
        potentials = new Set();
        // Gather the type of each enum values, and use that as a starter for potential types.
        for (const v of schema.enum) {
            switch (typeof v) {
                case 'string':
                case 'number':
                case 'boolean':
                    potentials.add(typeof v);
                    break;
                case 'object':
                    if (Array.isArray(v)) {
                        potentials.add('array');
                    }
                    else if (v === null) {
                        potentials.add('null');
                    }
                    else {
                        potentials.add('object');
                    }
                    break;
            }
        }
    }
    else {
        potentials = new Set(allTypes);
    }
    if (interface_1.isJsonObject(schema.not)) {
        const notTypes = getTypesOfSchema(schema.not);
        potentials = new Set([...potentials].filter(p => !notTypes.has(p)));
    }
    if (Array.isArray(schema.allOf)) {
        for (const sub of schema.allOf) {
            const types = getTypesOfSchema(sub);
            potentials = new Set([...potentials].filter(p => types.has(p)));
        }
    }
    if (Array.isArray(schema.oneOf)) {
        let options = new Set();
        for (const sub of schema.oneOf) {
            const types = getTypesOfSchema(sub);
            options = new Set([...options, ...types]);
        }
        potentials = new Set([...potentials].filter(p => options.has(p)));
    }
    if (Array.isArray(schema.anyOf)) {
        let options = new Set();
        for (const sub of schema.anyOf) {
            const types = getTypesOfSchema(sub);
            options = new Set([...options, ...types]);
        }
        potentials = new Set([...potentials].filter(p => options.has(p)));
    }
    if (schema.properties) {
        potentials.add('object');
    }
    else if (schema.items) {
        potentials.add('array');
    }
    return potentials;
}
exports.getTypesOfSchema = getTypesOfSchema;
//# sourceMappingURL=data:application/json;base64,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