input {
  # 接收来自应用的日志
  http {
    port => 5000
    codec => json
  }
  
  # 监听日志文件
  file {
    path => "/usr/share/logstash/logs/*.log"
    start_position => "beginning"
    codec => json
  }
}

filter {
  # 解析时间戳
  date {
    match => [ "@timestamp", "ISO8601" ]
  }
  
  # 添加字段
  mutate {
    add_field => { "[@metadata][index]" => "wht-saas-logs" }
  }
  
  # 处理错误日志
  if [level] == "error" {
    mutate {
      add_tag => [ "error" ]
    }
  }
  
  # 处理 API 请求日志
  if [url] {
    mutate {
      add_tag => [ "api_request" ]
    }
  }
}

output {
  # 输出到 Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "%{[@metadata][index]}-%{+YYYY.MM.dd}"
    template_name => "wht-saas-logs"
    template_pattern => "wht-saas-logs-*"
    template => {
      "index_patterns" => ["wht-saas-logs-*"],
      "settings" => {
        "number_of_shards" => 1,
        "number_of_replicas" => 0
      },
      "mappings" => {
        "properties" => {
          "@timestamp" => { "type" => "date" },
          "level" => { "type" => "keyword" },
          "message" => { "type" => "text" },
          "service" => { "type" => "keyword" },
          "environment" => { "type" => "keyword" },
          "traceId" => { "type" => "keyword" },
          "correlationId" => { "type" => "keyword" },
          "userId" => { "type" => "keyword" },
          "method" => { "type" => "keyword" },
          "url" => { "type" => "keyword" },
          "statusCode" => { "type" => "integer" },
          "responseTime" => { "type" => "integer" },
          "ip" => { "type" => "ip" }
        }
      }
    }
  }
  
  # 调试输出到控制台
  stdout {
    codec => rubydebug
  }
}
