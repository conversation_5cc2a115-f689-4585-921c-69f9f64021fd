#!/bin/bash

# SonarQube 设置脚本

echo "正在启动 SonarQube 服务..."

# 启动 SonarQube 和数据库
docker-compose -f docker-compose.dev.yml up -d db sonarqube

echo "等待 SonarQube 启动..."
sleep 30

# 检查 SonarQube 是否启动成功
max_attempts=12
attempt=0
sonar_ready=false

while [ $attempt -lt $max_attempts ] && [ "$sonar_ready" = false ]; do
    if curl -s http://localhost:9000/api/system/status | grep -q '"status":"UP"'; then
        sonar_ready=true
        echo "SonarQube 启动成功!"
    else
        echo "等待 SonarQube 启动... (尝试 $((attempt + 1))/$max_attempts)"
        sleep 10
        ((attempt++))
    fi
done

if [ "$sonar_ready" = false ]; then
    echo "SonarQube 启动失败，请检查日志"
    exit 1
fi

echo "SonarQube 配置完成!"
echo "访问地址: http://localhost:9000"
echo "默认用户名: admin"
echo "默认密码: admin"
echo ""
echo "请按照以下步骤完成配置:"
echo "1. 访问 http://localhost:9000"
echo "2. 使用 admin/admin 登录"
echo "3. 修改默认密码"
echo "4. 创建项目 token"
echo "5. 运行 'pnpm run quality:check' 进行代码扫描"
