{"version": 3, "names": ["_path", "data", "require", "_configDescriptors", "createItemFromDescriptor", "desc", "ConfigItem", "createConfigItem", "value", "dirname", "type", "descriptor", "createDescriptor", "path", "resolve", "alias", "CONFIG_ITEM_BRAND", "Symbol", "for", "getItemDescriptor", "item", "_descriptor", "undefined", "constructor", "options", "name", "file", "Object", "defineProperty", "enumerable", "request", "resolved", "freeze", "prototype"], "sources": ["../../src/config/item.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from \"gensync\";\nimport type { PluginTarget, PluginOptions } from \"./validation/options.ts\";\n\nimport path from \"node:path\";\nimport { createDescriptor } from \"./config-descriptors.ts\";\n\nimport type { UnloadedDescriptor } from \"./config-descriptors.ts\";\n\nexport function createItemFromDescriptor<API>(\n  desc: UnloadedDescriptor<API>,\n): ConfigItem<API> {\n  return new ConfigItem(desc);\n}\n\n/**\n * Create a config item using the same value format used in Babel's config\n * files. Items returned from this function should be cached by the caller\n * ideally, as recreating the config item will mean re-resolving the item\n * and re-evaluating the plugin/preset function.\n */\nexport function* createConfigItem<API>(\n  value:\n    | PluginTarget\n    | [PluginTarget, PluginOptions]\n    | [PluginTarget, PluginOptions, string | void],\n  {\n    dirname = \".\",\n    type,\n  }: {\n    dirname?: string;\n    type?: \"preset\" | \"plugin\";\n  } = {},\n): Handler<ConfigItem<API>> {\n  const descriptor = yield* createDescriptor(value, path.resolve(dirname), {\n    type,\n    alias: \"programmatic item\",\n  });\n\n  return createItemFromDescriptor(descriptor);\n}\n\nconst CONFIG_ITEM_BRAND = Symbol.for(\"@babel/core@7 - ConfigItem\");\n\nexport function getItemDescriptor<API>(\n  item: unknown,\n): UnloadedDescriptor<API> | void {\n  if ((item as any)?.[CONFIG_ITEM_BRAND]) {\n    return (item as ConfigItem<API>)._descriptor;\n  }\n\n  return undefined;\n}\n\nexport type { ConfigItem };\n\n/**\n * A public representation of a plugin/preset that will _eventually_ be load.\n * Users can use this to interact with the results of a loaded Babel\n * configuration.\n *\n * Any changes to public properties of this class should be considered a\n * breaking change to Babel's API.\n */\nclass ConfigItem<API> {\n  /**\n   * The private underlying descriptor that Babel actually cares about.\n   * If you access this, you are a bad person.\n   */\n  _descriptor: UnloadedDescriptor<API>;\n\n  // TODO(Babel 9): Check if this symbol needs to be updated\n  /**\n   * Used to detect ConfigItem instances from other Babel instances.\n   */\n  [CONFIG_ITEM_BRAND] = true;\n\n  /**\n   * The resolved value of the item itself.\n   */\n  value: object | Function;\n\n  /**\n   * The options, if any, that were passed to the item.\n   * Mutating this will lead to undefined behavior.\n   *\n   * \"false\" means that this item has been disabled.\n   */\n  options: object | void | false;\n\n  /**\n   * The directory that the options for this item are relative to.\n   */\n  dirname: string;\n\n  /**\n   * Get the name of the plugin, if the user gave it one.\n   */\n  name: string | void;\n\n  /**\n   * Data about the file that the item was loaded from, if Babel knows it.\n   */\n  file: {\n    // The requested path, e.g. \"@babel/env\".\n    request: string;\n    // The resolved absolute path of the file.\n    resolved: string;\n  } | void;\n\n  constructor(descriptor: UnloadedDescriptor<API>) {\n    // Make people less likely to stumble onto this if they are exploring\n    // programmatically, and also make sure that if people happen to\n    // pass the item through JSON.stringify, it doesn't show up.\n    this._descriptor = descriptor;\n    Object.defineProperty(this, \"_descriptor\", { enumerable: false });\n\n    Object.defineProperty(this, CONFIG_ITEM_BRAND, { enumerable: false });\n\n    this.value = this._descriptor.value;\n    this.options = this._descriptor.options;\n    this.dirname = this._descriptor.dirname;\n    this.name = this._descriptor.name;\n    this.file = this._descriptor.file\n      ? {\n          request: this._descriptor.file.request,\n          resolved: this._descriptor.file.resolved,\n        }\n      : undefined;\n\n    // Freeze the object to make it clear that people shouldn't expect mutating\n    // this object to do anything. A new item should be created if they want\n    // to change something.\n    Object.freeze(this);\n  }\n}\n\nObject.freeze(ConfigItem.prototype);\n"], "mappings": ";;;;;;;;AAGA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,IAAAE,kBAAA,GAAAD,OAAA;AAIO,SAASE,wBAAwBA,CACtCC,IAA6B,EACZ;EACjB,OAAO,IAAIC,UAAU,CAACD,IAAI,CAAC;AAC7B;AAQO,UAAUE,gBAAgBA,CAC/BC,KAGgD,EAChD;EACEC,OAAO,GAAG,GAAG;EACbC;AAIF,CAAC,GAAG,CAAC,CAAC,EACoB;EAC1B,MAAMC,UAAU,GAAG,OAAO,IAAAC,mCAAgB,EAACJ,KAAK,EAAEK,MAAGA,CAAC,CAACC,OAAO,CAACL,OAAO,CAAC,EAAE;IACvEC,IAAI;IACJK,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,OAAOX,wBAAwB,CAACO,UAAU,CAAC;AAC7C;AAEA,MAAMK,iBAAiB,GAAGC,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC;AAE3D,SAASC,iBAAiBA,CAC/BC,IAAa,EACmB;EAChC,IAAKA,IAAI,YAAJA,IAAI,CAAWJ,iBAAiB,CAAC,EAAE;IACtC,OAAQI,IAAI,CAAqBC,WAAW;EAC9C;EAEA,OAAOC,SAAS;AAClB;AAYA,MAAMhB,UAAU,CAAM;EA8CpBiB,WAAWA,CAACZ,UAAmC,EAAE;IAAA,KAzCjDU,WAAW;IAAA,KAMVL,iBAAiB,IAAI,IAAI;IAAA,KAK1BR,KAAK;IAAA,KAQLgB,OAAO;IAAA,KAKPf,OAAO;IAAA,KAKPgB,IAAI;IAAA,KAKJC,IAAI;IAWF,IAAI,CAACL,WAAW,GAAGV,UAAU;IAC7BgB,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE;MAAEC,UAAU,EAAE;IAAM,CAAC,CAAC;IAEjEF,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEZ,iBAAiB,EAAE;MAAEa,UAAU,EAAE;IAAM,CAAC,CAAC;IAErE,IAAI,CAACrB,KAAK,GAAG,IAAI,CAACa,WAAW,CAACb,KAAK;IACnC,IAAI,CAACgB,OAAO,GAAG,IAAI,CAACH,WAAW,CAACG,OAAO;IACvC,IAAI,CAACf,OAAO,GAAG,IAAI,CAACY,WAAW,CAACZ,OAAO;IACvC,IAAI,CAACgB,IAAI,GAAG,IAAI,CAACJ,WAAW,CAACI,IAAI;IACjC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACL,WAAW,CAACK,IAAI,GAC7B;MACEI,OAAO,EAAE,IAAI,CAACT,WAAW,CAACK,IAAI,CAACI,OAAO;MACtCC,QAAQ,EAAE,IAAI,CAACV,WAAW,CAACK,IAAI,CAACK;IAClC,CAAC,GACDT,SAAS;IAKbK,MAAM,CAACK,MAAM,CAAC,IAAI,CAAC;EACrB;AACF;AAEAL,MAAM,CAACK,MAAM,CAAC1B,UAAU,CAAC2B,SAAS,CAAC;AAAC", "ignoreList": []}