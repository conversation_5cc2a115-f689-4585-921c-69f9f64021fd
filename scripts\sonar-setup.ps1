# SonarQube 设置脚本 (PowerShell)

Write-Host "正在启动 SonarQube 服务..." -ForegroundColor Green

# 启动 SonarQube 和数据库
docker-compose -f docker-compose.dev.yml up -d db sonarqube

Write-Host "等待 SonarQube 启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 检查 SonarQube 是否启动成功
$maxAttempts = 12
$attempt = 0
$sonarReady = $false

while ($attempt -lt $maxAttempts -and -not $sonarReady) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:9000/api/system/status" -UseBasicParsing
        $status = ($response.Content | ConvertFrom-Json).status
        if ($status -eq "UP") {
            $sonarReady = $true
            Write-Host "SonarQube 启动成功!" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "等待 SonarQube 启动... (尝试 $($attempt + 1)/$maxAttempts)" -ForegroundColor Yellow
        Start-Sleep -Seconds 10
        $attempt++
    }
}

if (-not $sonarReady) {
    Write-Host "SonarQube 启动失败，请检查日志" -ForegroundColor Red
    exit 1
}

Write-Host "SonarQube 配置完成!" -ForegroundColor Green
Write-Host "访问地址: http://localhost:9000" -ForegroundColor Cyan
Write-Host "默认用户名: admin" -ForegroundColor Cyan
Write-Host "默认密码: admin" -ForegroundColor Cyan
Write-Host "" 
Write-Host "请按照以下步骤完成配置:" -ForegroundColor Yellow
Write-Host "1. 访问 http://localhost:9000" -ForegroundColor White
Write-Host "2. 使用 admin/admin 登录" -ForegroundColor White
Write-Host "3. 修改默认密码" -ForegroundColor White
Write-Host "4. 创建项目 token" -ForegroundColor White
Write-Host "5. 运行 'pnpm run quality:check' 进行代码扫描" -ForegroundColor White
