"use strict";
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function __export(m) {
    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];
}
Object.defineProperty(exports, "__esModule", { value: true });
__export(require("./alias"));
__export(require("./buffer"));
__export(require("./empty"));
__export(require("./interface"));
__export(require("./memory"));
__export(require("./pattern"));
__export(require("./record"));
__export(require("./safe"));
__export(require("./scoped"));
__export(require("./sync"));
__export(require("./resolver"));
__export(require("./test"));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiLi8iLCJzb3VyY2VzIjpbInBhY2thZ2VzL2FuZ3VsYXJfZGV2a2l0L2NvcmUvc3JjL3ZpcnR1YWwtZnMvaG9zdC9pbmRleC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUE7Ozs7OztHQU1HOzs7OztBQUVILDZCQUF3QjtBQUN4Qiw4QkFBeUI7QUFDekIsNkJBQXdCO0FBQ3hCLGlDQUE0QjtBQUM1Qiw4QkFBeUI7QUFDekIsK0JBQTBCO0FBQzFCLDhCQUF5QjtBQUN6Qiw0QkFBdUI7QUFDdkIsOEJBQXlCO0FBQ3pCLDRCQUF1QjtBQUN2QixnQ0FBMkI7QUFDM0IsNEJBQXVCIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBJbmMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5leHBvcnQgKiBmcm9tICcuL2FsaWFzJztcbmV4cG9ydCAqIGZyb20gJy4vYnVmZmVyJztcbmV4cG9ydCAqIGZyb20gJy4vZW1wdHknO1xuZXhwb3J0ICogZnJvbSAnLi9pbnRlcmZhY2UnO1xuZXhwb3J0ICogZnJvbSAnLi9tZW1vcnknO1xuZXhwb3J0ICogZnJvbSAnLi9wYXR0ZXJuJztcbmV4cG9ydCAqIGZyb20gJy4vcmVjb3JkJztcbmV4cG9ydCAqIGZyb20gJy4vc2FmZSc7XG5leHBvcnQgKiBmcm9tICcuL3Njb3BlZCc7XG5leHBvcnQgKiBmcm9tICcuL3N5bmMnO1xuZXhwb3J0ICogZnJvbSAnLi9yZXNvbHZlcic7XG5leHBvcnQgKiBmcm9tICcuL3Rlc3QnO1xuIl19