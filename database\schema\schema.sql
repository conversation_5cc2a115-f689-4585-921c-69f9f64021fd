-- 完整数据库结构定义
-- 这个文件包含完整的数据库结构，用于快速创建新的数据库实例

-- 在这里定义完整的数据库结构
-- 包括所有表、索引、约束、触发器等

-- 示例结构（请根据实际需求修改）:
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- 
-- -- 用户表
-- CREATE TABLE IF NOT EXISTS users (
--     id SERIAL PRIMARY KEY,
--     uuid UUID DEFAULT uuid_generate_v4(),
--     username VARCHAR(50) UNIQUE NOT NULL,
--     email VARCHAR(100) UNIQUE NOT NULL,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );
-- 
-- -- 任务表
-- CREATE TABLE IF NOT EXISTS tasks (
--     id SERIAL PRIMARY KEY,
--     uuid UUID DEFAULT uuid_generate_v4(),
--     title VARCHAR(255) NOT NULL,
--     description TEXT,
--     status VARCHAR(20) DEFAULT 'pending',
--     user_id INTEGER REFERENCES users(id),
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );
