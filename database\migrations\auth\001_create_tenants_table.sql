-- 创建租户表
-- 创建时间: 2024-01-01
-- 描述: 多租户系统的租户基础表

CREATE TABLE IF NOT EXISTS tenants (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT '租户名称',
    org_code VARCHAR(100) NOT NULL UNIQUE COMMENT '组织编码（唯一）',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-冻结',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建索引
CREATE INDEX idx_tenants_org_code ON tenants(org_code);
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_created_at ON tenants(created_at);

-- 添加表注释
COMMENT ON TABLE tenants IS '租户表 - 多租户系统的租户基础信息';

-- 添加字段注释
COMMENT ON COLUMN tenants.id IS '租户ID，主键';
COMMENT ON COLUMN tenants.name IS '租户名称';
COMMENT ON COLUMN tenants.org_code IS '组织编码，全局唯一';
COMMENT ON COLUMN tenants.status IS '租户状态：1-正常，2-冻结';
COMMENT ON COLUMN tenants.created_at IS '创建时间';
COMMENT ON COLUMN tenants.updated_at IS '更新时间';

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tenants_updated_at 
    BEFORE UPDATE ON tenants 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
