version: '3.8'
services:
  backend:
    build:
      context: ./apps/backend
      dockerfile: backend-dev.dockerfile
    command: pnpm run start:dev
    volumes:
      - ./apps/backend:/app
      - /app/node_modules
    ports:
      - "4000:4000"
    environment:
      DATABASE_URL: ****************************/yourdb
      NODE_ENV: development
      PORT: 4000
      JWT_SECRET: your-dev-jwt-secret-key
      CORS_ORIGIN: http://localhost:3000
    depends_on:
      - db
    restart: unless-stopped

  frontend:
    build:
      context: ./apps/frontend
      dockerfile: frontend-dev.dockerfile
    command: pnpm run dev:webpack
    volumes:
      - ./apps/frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:4000
      NODE_ENV: development
    depends_on:
      - backend
    restart: unless-stopped

  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
      POSTGRES_DB: yourdb
      POSTGRES_MULTIPLE_DATABASES: sonarqube
    ports:
      - "5432:5432"
    volumes:
      - pg_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./init-sonar-db.sh:/docker-entrypoint-initdb.d/init-sonar-db.sh

  sonarqube:
    image: sonarqube:10.6-community
    restart: unless-stopped
    environment:
      SONAR_JDBC_URL: ***********************************
      SONAR_JDBC_USERNAME: user
      SONAR_JDBC_PASSWORD: pass
    ports:
      - "9000:9000"
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
    depends_on:
      - db

volumes:
  pg_data:
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
