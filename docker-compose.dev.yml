version: '3.8'
services:
  backend:
    build:
      context: ./apps/backend
      dockerfile: backend-dev.dockerfile
    command: pnpm run start:dev
    volumes:
      - ./apps/backend:/app
      - /app/node_modules
    ports:
      - "4000:4000"
    environment:
      DATABASE_URL: ****************************/yourdb
      NODE_ENV: development
      PORT: 4000
      JWT_SECRET: your-dev-jwt-secret-key
      CORS_ORIGIN: http://localhost:3000
    depends_on:
      - db
    restart: unless-stopped

  frontend:
    build:
      context: ./apps/frontend
      dockerfile: frontend-dev.dockerfile
    command: pnpm run dev:webpack
    volumes:
      - ./apps/frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:4000
      NODE_ENV: development
    depends_on:
      - backend
    restart: unless-stopped

  db:
    image: registry.cn-hangzhou.aliyuncs.com/library/postgres:15
    restart: always
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
      POSTGRES_DB: yourdb
    ports:
      - "5432:5432"
    volumes:
      - pg_data:/var/lib/postgresql/data
      - ./database/migrations:/docker-entrypoint-initdb.d/migrations
      - ./database/seeders:/docker-entrypoint-initdb.d/seeders
      - ./database/scripts/init-sonar-db.sh:/docker-entrypoint-initdb.d/01-init-sonar-db.sh
      - ./database/scripts/init-database.sh:/docker-entrypoint-initdb.d/02-init-database.sh

  sonarqube:
    image: registry.cn-hangzhou.aliyuncs.com/library/sonarqube:9.9-community
    restart: unless-stopped
    environment:
      SONAR_JDBC_URL: ***********************************
      SONAR_JDBC_USERNAME: user
      SONAR_JDBC_PASSWORD: pass
    ports:
      - "9000:9000"
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
    depends_on:
      - db

  elasticsearch:
    image: registry.cn-hangzhou.aliyuncs.com/library/elasticsearch:8.13.4
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - es_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  logstash:
    image: registry.cn-hangzhou.aliyuncs.com/library/logstash:8.13.4
    restart: unless-stopped
    ports:
      - "5000:5000"
      - "5044:5044"
    volumes:
      - ./elk/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./apps/backend/logs:/usr/share/logstash/logs
    environment:
      - "LS_JAVA_OPTS=-Xmx256m -Xms256m"
    depends_on:
      elasticsearch:
        condition: service_healthy

  kibana:
    image: registry.cn-hangzhou.aliyuncs.com/library/kibana:8.13.4
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      elasticsearch:
        condition: service_healthy

volumes:
  pg_data:
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
  es_data:
