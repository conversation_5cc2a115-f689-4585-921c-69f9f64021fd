version: '3.8'
services:
  backend:
    build:
      context: ./apps/backend
      dockerfile: backend-dev.dockerfile
    command: pnpm run start:dev
    volumes:
      - ./apps/backend:/app
      - /app/node_modules
    ports:
      - "4000:4000"
    environment:
      DATABASE_URL: ****************************/yourdb
      NODE_ENV: development
      PORT: 4000
      JWT_SECRET: your-dev-jwt-secret-key
      CORS_ORIGIN: http://localhost:3000
    depends_on:
      - db
    restart: unless-stopped

  frontend:
    build:
      context: ./apps/frontend
      dockerfile: frontend-dev.dockerfile
    command: pnpm run dev:webpack
    volumes:
      - ./apps/frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:4000
      NODE_ENV: development
    depends_on:
      - backend
    restart: unless-stopped

  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
      POSTGRES_DB: yourdb
    ports:
      - "5432:5432"
    volumes:
      - pg_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

volumes:
  pg_data:
