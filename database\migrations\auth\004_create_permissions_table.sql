-- 创建权限表
-- 创建时间: 2024-01-01
-- 描述: 系统权限表，全局权限定义

CREATE TABLE IF NOT EXISTS permissions (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码（全局唯一）',
    description VARCHAR(500) NULL COMMENT '权限说明',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建索引
CREATE INDEX idx_permissions_code ON permissions(code);
CREATE INDEX idx_permissions_name ON permissions(name);
CREATE INDEX idx_permissions_created_at ON permissions(created_at);

-- 添加表注释
COMMENT ON TABLE permissions IS '权限表 - 系统全局权限定义，不区分租户';

-- 添加字段注释
COMMENT ON COLUMN permissions.id IS '权限ID，主键';
COMMENT ON COLUMN permissions.name IS '权限名称，便于理解的权限描述';
COMMENT ON COLUMN permissions.code IS '权限编码，全局唯一，用于程序权限检查';
COMMENT ON COLUMN permissions.description IS '权限详细说明';
COMMENT ON COLUMN permissions.created_at IS '创建时间';
COMMENT ON COLUMN permissions.updated_at IS '更新时间';

-- 创建更新时间触发器
CREATE TRIGGER update_permissions_updated_at 
    BEFORE UPDATE ON permissions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
