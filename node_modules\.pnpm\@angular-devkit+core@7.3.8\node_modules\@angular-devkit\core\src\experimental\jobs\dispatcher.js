"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const api_1 = require("./api");
const exception_1 = require("./exception");
/**
 * OnReady a dispatcher that can dispatch to a sub job, depending on conditions.
 * @param options
 */
function createDispatcher(options = {}) {
    let defaultDelegate = null;
    const conditionalDelegateList = [];
    const job = Object.assign((argument, context) => {
        const maybeDelegate = conditionalDelegateList.find(([predicate]) => predicate(argument));
        let delegate = null;
        if (maybeDelegate) {
            delegate = context.scheduler.schedule(maybeDelegate[1], argument);
        }
        else if (defaultDelegate) {
            delegate = context.scheduler.schedule(defaultDelegate, argument);
        }
        else {
            throw new exception_1.JobDoesNotExistException('<null>');
        }
        context.inboundBus.subscribe(delegate.inboundBus);
        return delegate.outboundBus;
    }, {
        jobDescription: options,
    });
    return Object.assign(job, {
        setDefaultJob(name) {
            if (api_1.is<PERSON><PERSON><PERSON><PERSON><PERSON>(name)) {
                name = name.jobDescription.name === undefined ? null : name.jobDescription.name;
            }
            defaultDelegate = name;
        },
        addConditionalJob(predicate, name) {
            conditionalDelegateList.push([predicate, name]);
        },
    });
}
exports.createDispatcher = createDispatcher;
//# sourceMappingURL=data:application/json;base64,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