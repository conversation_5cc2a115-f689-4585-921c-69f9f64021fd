-- 数据库初始化脚本
-- 创建开发环境所需的基础表结构

-- 创建用户表示例
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建任务表示例
CREATE TABLE IF NOT EXISTS tasks (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入测试数据
INSERT INTO users (username, email, password_hash) VALUES 
('testuser', '<EMAIL>', '$2b$10$example.hash.here')
ON CONFLICT (username) DO NOTHING;

INSERT INTO tasks (title, description, status, user_id) VALUES 
('示例任务', '这是一个示例任务', 'pending', 1)
ON CONFLICT DO NOTHING;
