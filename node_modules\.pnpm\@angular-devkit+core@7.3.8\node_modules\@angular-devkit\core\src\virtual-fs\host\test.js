"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const path_1 = require("../path");
const buffer_1 = require("./buffer");
const memory_1 = require("./memory");
const sync_1 = require("./sync");
var test;
(function (test) {
    class TestHost extends memory_1.SimpleMemoryHost {
        constructor(map = {}) {
            super();
            this._records = [];
            for (const filePath of Object.getOwnPropertyNames(map)) {
                this._write(path_1.normalize(filePath), buffer_1.stringToFileBuffer(map[filePath]));
            }
        }
        get records() {
            return [...this._records];
        }
        clearRecords() {
            this._records = [];
        }
        get files() {
            const sync = this.sync;
            function _visit(p) {
                return sync.list(p)
                    .map(fragment => path_1.join(p, fragment))
                    .reduce((files, path) => {
                    if (sync.isDirectory(path)) {
                        return files.concat(_visit(path));
                    }
                    else {
                        return files.concat(path);
                    }
                }, []);
            }
            return _visit(path_1.normalize('/'));
        }
        get sync() {
            if (!this._sync) {
                this._sync = new sync_1.SyncDelegateHost(this);
            }
            return this._sync;
        }
        clone() {
            const newHost = new TestHost();
            newHost._cache = new Map(this._cache);
            return newHost;
        }
        // Override parents functions to keep a record of all operators that were done.
        _write(path, content) {
            this._records.push({ kind: 'write', path });
            return super._write(path, content);
        }
        _read(path) {
            this._records.push({ kind: 'read', path });
            return super._read(path);
        }
        _delete(path) {
            this._records.push({ kind: 'delete', path });
            return super._delete(path);
        }
        _rename(from, to) {
            this._records.push({ kind: 'rename', from, to });
            return super._rename(from, to);
        }
        _list(path) {
            this._records.push({ kind: 'list', path });
            return super._list(path);
        }
        _exists(path) {
            this._records.push({ kind: 'exists', path });
            return super._exists(path);
        }
        _isDirectory(path) {
            this._records.push({ kind: 'isDirectory', path });
            return super._isDirectory(path);
        }
        _isFile(path) {
            this._records.push({ kind: 'isFile', path });
            return super._isFile(path);
        }
        _stat(path) {
            this._records.push({ kind: 'stat', path });
            return super._stat(path);
        }
        _watch(path, options) {
            this._records.push({ kind: 'watch', path });
            return super._watch(path, options);
        }
        $write(path, content) {
            return super._write(path_1.normalize(path), buffer_1.stringToFileBuffer(content));
        }
        $read(path) {
            return buffer_1.fileBufferToString(super._read(path_1.normalize(path)));
        }
        $list(path) {
            return super._list(path_1.normalize(path));
        }
        $exists(path) {
            return super._exists(path_1.normalize(path));
        }
        $isDirectory(path) {
            return super._isDirectory(path_1.normalize(path));
        }
        $isFile(path) {
            return super._isFile(path_1.normalize(path));
        }
    }
    test.TestHost = TestHost;
})(test = exports.test || (exports.test = {}));
//# sourceMappingURL=data:application/json;base64,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