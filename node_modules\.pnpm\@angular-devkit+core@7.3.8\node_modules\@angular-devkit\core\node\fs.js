"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const fs_1 = require("fs");
function isFile(filePath) {
    let stat;
    try {
        stat = fs_1.statSync(filePath);
    }
    catch (e) {
        if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) {
            return false;
        }
        throw e;
    }
    return stat.isFile() || stat.isFIFO();
}
exports.isFile = isFile;
function isDirectory(filePath) {
    let stat;
    try {
        stat = fs_1.statSync(filePath);
    }
    catch (e) {
        if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) {
            return false;
        }
        throw e;
    }
    return stat.isDirectory();
}
exports.isDirectory = isDirectory;
//# sourceMappingURL=data:application/json;base64,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