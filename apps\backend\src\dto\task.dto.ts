import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsNotEmpty, MaxLength } from 'class-validator';

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export class CreateTaskDto {
  @ApiProperty({ 
    description: '任务标题', 
    example: '完成项目文档',
    maxLength: 255
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title: string;

  @ApiPropertyOptional({ 
    description: '任务描述', 
    example: '编写项目的技术文档和用户手册'
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ 
    description: '任务状态', 
    enum: TaskStatus,
    example: TaskStatus.PENDING
  })
  @IsEnum(TaskStatus)
  @IsOptional()
  status?: TaskStatus = TaskStatus.PENDING;
}

export class UpdateTaskDto {
  @ApiPropertyOptional({ 
    description: '任务标题', 
    example: '完成项目文档',
    maxLength: 255
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  title?: string;

  @ApiPropertyOptional({ 
    description: '任务描述', 
    example: '编写项目的技术文档和用户手册'
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ 
    description: '任务状态', 
    enum: TaskStatus,
    example: TaskStatus.IN_PROGRESS
  })
  @IsEnum(TaskStatus)
  @IsOptional()
  status?: TaskStatus;
}

export class TaskResponseDto {
  @ApiProperty({ description: '任务ID', example: 1 })
  id: number;

  @ApiProperty({ description: '任务标题', example: '完成项目文档' })
  title: string;

  @ApiProperty({ description: '任务描述', example: '编写项目的技术文档和用户手册' })
  description: string;

  @ApiProperty({ 
    description: '任务状态', 
    enum: TaskStatus,
    example: TaskStatus.PENDING
  })
  status: TaskStatus;

  @ApiProperty({ description: '创建时间', example: '2024-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2024-01-01T00:00:00.000Z' })
  updatedAt: Date;
}

export class TaskListResponseDto {
  @ApiProperty({ type: [TaskResponseDto], description: '任务列表' })
  data: TaskResponseDto[];

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;
}
