-- 创建角色-权限关联表
-- 创建时间: 2024-01-01
-- 描述: 角色和权限的多对多关联表

CREATE TABLE IF NOT EXISTS role_permissions (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 外键约束
    CONSTRAINT fk_role_permissions_role_id FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    CONSTRAINT fk_role_permissions_permission_id FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- 创建唯一性约束（防止重复关联）
ALTER TABLE role_permissions ADD CONSTRAINT uk_role_permissions_role_permission UNIQUE (role_id, permission_id);

-- 创建索引
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX idx_role_permissions_created_at ON role_permissions(created_at);

-- 添加表注释
COMMENT ON TABLE role_permissions IS '角色权限关联表 - 角色和权限的多对多关系';

-- 添加字段注释
COMMENT ON COLUMN role_permissions.id IS '关联ID，主键';
COMMENT ON COLUMN role_permissions.role_id IS '角色ID，外键关联roles表';
COMMENT ON COLUMN role_permissions.permission_id IS '权限ID，外键关联permissions表';
COMMENT ON COLUMN role_permissions.created_at IS '关联创建时间';
