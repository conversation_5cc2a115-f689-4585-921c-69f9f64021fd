{"name": "wht-saas", "version": "1.0.0", "private": true, "scripts": {"dev": "pnpm --parallel --filter frontend --filter backend run dev", "dev:frontend": "pnpm --filter frontend run dev", "dev:backend": "pnpm --filter backend run start:dev", "build": "pnpm --filter frontend --filter backend run build", "build:frontend": "pnpm --filter frontend run build", "build:backend": "pnpm --filter backend run build", "start": "pnpm --parallel --filter frontend --filter backend run start", "start:frontend": "pnpm --filter frontend run start", "start:backend": "pnpm --filter backend run start", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build", "docker:prod": "docker-compose up --build", "docker:down": "docker-compose -f docker-compose.dev.yml down"}, "devDependencies": {"typescript": "^5.8.3"}}