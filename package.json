{"name": "wht-saas", "version": "1.0.0", "private": true, "scripts": {"dev": "pnpm --parallel --filter frontend --filter backend run dev", "dev:frontend": "pnpm --filter frontend run dev", "dev:backend": "pnpm --filter backend run start:dev", "build": "pnpm --filter frontend --filter backend run build", "build:frontend": "pnpm --filter frontend run build", "build:backend": "pnpm --filter backend run build", "start": "pnpm --parallel --filter frontend --filter backend run start", "start:frontend": "pnpm --filter frontend run start", "start:backend": "pnpm --filter backend run start", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build", "docker:prod": "docker-compose up --build", "docker:down": "docker-compose -f docker-compose.dev.yml down", "db:reset": "node database/scripts/reset-db.js", "db:start": "docker-compose -f docker-compose.dev.yml up -d db", "db:stop": "docker-compose -f docker-compose.dev.yml stop db", "db:logs": "docker-compose -f docker-compose.dev.yml logs -f db", "dev:clean": "pnpm run db:reset && pnpm run docker:dev", "test:coverage": "pnpm --filter frontend --filter backend run test:cov", "sonar:scan": "sonar-scanner", "sonar:setup": "node -e \"process.platform === 'win32' ? require('child_process').exec('powershell -ExecutionPolicy Bypass -File scripts/sonar-setup.ps1') : require('child_process').exec('bash scripts/sonar-setup.sh')\"", "sonar:start": "docker-compose -f docker-compose.dev.yml up -d sonarqube db", "sonar:stop": "docker-compose -f docker-compose.dev.yml stop sonarqube", "quality:check": "pnpm run test:coverage && pnpm run sonar:scan", "elk:start": "docker-compose -f docker-compose.dev.yml up -d elasticsearch logstash kibana", "elk:stop": "docker-compose -f docker-compose.dev.yml stop elasticsearch logstash kibana", "elk:logs": "docker-compose -f docker-compose.dev.yml logs -f logstash", "logs:view": "docker-compose -f docker-compose.dev.yml logs -f backend"}, "devDependencies": {"typescript": "^5.8.3", "sonarqube-scanner": "^4.0.1"}}